import * as yup from 'yup';

// Function to generate dynamic schema from API data
export const generateNotificationsSchema = (apiData: any) => {
  const schemaShape: any = {};
  
  apiData.forEach((category: any) => {
    const categoryShape: any = {};
    
    category.items.forEach((item: any) => {
      const itemShape: any = {};
      
      // Only include validation for keys that exist in the value object
      if (item.value.hasOwnProperty('text')) {
        itemShape.text = yup.boolean();
      }
      if (item.value.hasOwnProperty('email')) {
        itemShape.email = yup.boolean();
      }
      if (item.value.hasOwnProperty('desktop')) {
        itemShape.desktop = yup.boolean();
      }
      
      categoryShape[item.event] = yup.object().shape(itemShape);
    });
    
    schemaShape[category.category] = yup.object().shape(categoryShape);
  });
  
  return yup.object().shape({
    notifications: yup.object().shape(schemaShape)
  });
};

// Default schema (will be replaced by dynamic one)
export const notificationsSchema = yup.object().shape({
  notifications: yup.object().shape({})
});

export type NotificationsFormData = yup.InferType<typeof notificationsSchema>; 