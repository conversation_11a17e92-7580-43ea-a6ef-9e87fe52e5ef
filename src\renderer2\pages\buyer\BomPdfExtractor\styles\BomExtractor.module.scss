.bomUploadContent {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0px 0px 0px 0px;
  position: relative;
  border: 2px dashed transparent;
  transition: all 0.3s ease;
  outline: none;

  &.dragActive {
    border: 2px dashed #1fbbfe;
    background-color: rgba(31, 187, 254, 0.05);
  }

  &.dragReject {
    border: 2px dashed #ff5252;
    background-color: rgba(255, 82, 82, 0.05);
  }

  &:focus-visible {
    outline: 2px solid #1fbbfe;
    box-shadow: 0 0 8px rgba(31, 187, 254, 0.5);
  }

  .bomUploadImage {
    height: 245px;
    width: 228px;

    img {
      height: 100%;
      width: 100%;
      object-fit: contain;
    }
  }

  .bomUploadText {
    background-image: linear-gradient(133deg, #191a20 -45%, rgba(221, 221, 221, 0.77) 60%);
    font-family: Inter;
    font-size: 28px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 1.12px;
    text-align: center;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 48px;
  }

  .filePreview {
    margin: 0 auto;
    background-color: transparent;
    border-radius: 8px;
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    text-align: center;

    .fileIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 76px;
      font-size: 40px;
      font-weight: bold;
      border-radius: 8px;
      margin-bottom: 20px;
      position: relative;
    }

    .pdfIcon,
    .excelIcon,
    .csvIcon,
    .defaultIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 76px;
      font-size: 24px;
      font-weight: bold;
      border-radius: 8px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 70px;
        height: 86px;
        border-radius: 8px;
        z-index: -1;
        right: -10px;
        bottom: -10px;
      }
    }

    .pdfIcon {
      background-color: #333;
      color: #fff;

      &::after {
        background-color: #222;
      }
    }

    .excelIcon {
      background-color: #ccc;
      color: #333;

      &::after {
        background-color: #919191;
      }
    }

    .csvIcon {
      background-color: #ddd;
      color: #333;

      &::after {
        background-color: #aaa;
      }
    }

    .defaultIcon {
      background-color: #999;
      color: #fff;

      &::after {
        background-color: #666;
      }
    }

    .fileName {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.15;
      letter-spacing: 0.98px;
      text-align: center;
      color: #fff;
      padding-bottom: 16px;
      padding-top: 24px;
    }

    .uploadButton {
      border-radius: 16px;
      border: solid 1px rgba(255, 255, 255, 0.5);
      width: 307px;
      height: 54px;

      &[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
      }

      span {
        background-image: linear-gradient(140deg, #191a20 -60%, #dcdcdc 63%);
        font-family: Inter;
        font-size: 24px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.96px;
        text-align: center;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      &:hover {
        -webkit-backdrop-filter: blur(21.3px);
        backdrop-filter: blur(21.3px);
        background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
      }

      &:focus-visible {
        -webkit-backdrop-filter: blur(21.3px);
        backdrop-filter: blur(21.3px);
        background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
      }
    }

    .uploadDifferentFileLink {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.15;
      letter-spacing: 0.98px;
      text-align: center;
      color: #8b8d91;
      padding-top: 24px;

      &:hover {
        color: #1fbbff;
      }
    }
  }

  .statusMessage {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: rgba(31, 187, 254, 0.1);
    border-radius: 4px;
    color: #1fbbfe;
    font-size: 16px;
  }

  .errorMessage {
    margin-top: 20px;
    padding: 10px 40px 10px 20px;
    background-color: rgba(255, 82, 82, 0.1);
    border-radius: 4px;
    color: #ff5252;
    font-size: 16px;
    text-align: center;
    max-width: 80%;
    position: relative;

    .closeButton {
      position: absolute;
      top: 13px;
      right: 12px;
      width: 20px;
      height: 20px;
      background-color: rgba(255, 82, 82, 0.2);
      color: #ff5252;
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 10px;
      padding: 0;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 82, 82, 0.3);
        transform: scale(1.1);
      }

      &:focus-visible {
        outline: 2px solid #ff5252;
        box-shadow: 0 0 4px rgba(255, 82, 82, 0.5);
      }
    }
  }

  .buttonContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
  }

  .bomUploadButton {
    border-radius: 16px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    height: 42px;
    width: 228px;

    &:focus {
      -webkit-backdrop-filter: blur(21.3px);
      backdrop-filter: blur(21.3px);
      background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
    }

    &:hover {
      -webkit-backdrop-filter: blur(21.3px);
      backdrop-filter: blur(21.3px);
      background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
    }

    &:focus-visible {
      -webkit-backdrop-filter: blur(21.3px);
      backdrop-filter: blur(21.3px);
      background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
    }

    span {
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: 0.72px;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(115deg, #191a20 -23%, #dcdcdc 56%);
    }
  }

  .uploadNowButton {
    border-radius: 16px;
    border: solid 1px #1fbbfe;
    background-color: #1fbbfe;
    color: white;
    font-family: Inter;
    font-size: 24px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.96px;
    text-align: center;
    height: 54px;
    width: 307px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #1ca9e2;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    &:focus-visible {
      outline: 2px solid white;
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    }
  }

  .clearButton {
    border-radius: 16px;
    border: solid 1px rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.64px;
    text-align: center;
    height: 44px;
    width: 307px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.9);
    }

    &:focus-visible {
      outline: 2px solid #1fbbfe;
      box-shadow: 0 0 8px rgba(31, 187, 254, 0.5);
    }
  }

  .supportedFormats {
    margin-top: 30px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    text-align: center;
  }
}


/* App.css */

/* App.css - Global styles for the PDF Text Extractor application */

:root {
  --text-color: #ffffff;
  --button-background: #000000;
  --button-text: #ffffff;
  --button-hover: #222222;
  --button-disabled: #555555;
  --border-color: #666666;
  --success-background: #2a4d3e;
  --success-text: #b7ebd8;
  --warning-background: #4d432a;
  --warning-text: #ebe6b7;
  --error-background: #4d2a2a;
  --error-text: #ebb7b7;
  --table-header: #222222;
  --table-row-odd: #3a3a3a;
  --table-row-even: #444444;
  --table-border: #555555;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-color);
}

.appContainer {
  div {
    :focus {
      outline: none;
    }
  }

  max-width: 100%;
  margin: 0 auto;
  // padding: 32px 24px;
  justify-content: center;
  // overflow-y: auto;
  max-height: 100%;
  column-gap: 24px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.card {
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button styles */
.button {
  background-color: var(--button-background);
  color: var(--button-text);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.button:hover:not(:disabled) {
  background-color: var(--button-hover);
}

.button:disabled {
  background-color: var(--button-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Select styles */
select {
  background-color: var(--button-background);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 7px 16px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 14px;
}

/* Input styles */
.input[type="file"] {
  display: none;
}

/* Alert styles */
.alert {
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.alertSuccess {
  background-color: var(--success-background);
  color: var(--success-text);
  border: 1px solid var(--success-text);
}

.alertWarning {
  background-color: var(--warning-background);
  color: var(--warning-text);
  border: 1px solid var(--warning-text);
}

.alertError {
  background-color: var(--error-background);
  color: var(--error-text);
  border: 1px solid var(--error-text);
}

/* PDF filename styles */
.pdfFilename {
  background-color: var(--card-background);
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid var(--button-background);
  font-size: 16px;
}

/* PDF container styles */
.pdfContainer {
  position: relative;
  // border-radius: 4px;
  height: 100%;
  // justify-content: center;
  margin: 0 auto;
  white-space: nowrap;
  text-align: center;
  /* Center the content horizontally */
}

.pdfContainerMain {
  position: relative;
  // border-radius: 4px;
  height: 100%;
  margin-bottom: 20px;
  white-space: nowrap;
  text-align: center;
  /* Center the content horizontally */
}

.pdfContent {
  position: relative;
  display: inline-block;
  text-align: left;
  /* Reset text alignment for the content */
}

.pdfOverlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 10;
}

/* Magnifying glass styles */
.magnifyingGlass {
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  z-index: 1000;
  border: 2px solid white;
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  border: 1px solid var(--table-border);

  thead {
    background-color: var(--table-header);
  }

  th {
    padding: 10px;
    text-align: left;
    border-bottom: 2px solid var(--table-border);
  }

  td {
    padding: 8px 10px;
    border-bottom: 1px solid var(--table-border);
  }

  tbody tr:nth-child(odd) {
    background-color: var(--table-row-odd);
  }

  tbody tr:nth-child(even) {
    background-color: var(--table-row-even);
  }
}


/* PDF Controls styles */
.pdfControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  flex-wrap: wrap;
}

.rotationControls {
  display: flex;
  gap: 10px;
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px 0;
}

.pagination button {
  margin: 0 5px;
}

.pagination span {
  margin: 0 10px;
  color: var(--text-color);
}

.leftToolBar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  width: 90px;
  padding: 15px 0;
  gap: 10px;

  .uploadButton {
    width: 90px;
    height: 60px;
    flex-grow: 0;
    padding: 15px 7px;
    border-radius: 10px;
    background-image: linear-gradient(to right, #0f0f14 -12%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
    font-family: Syncopate;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: -0.7px;
    text-align: center;
    color: #8c8b99;
  }

  .cancelBtn {
    position: absolute;
    bottom: 48px;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: -0.7px;
    text-align: center;
    color: #8c8b99;

  }

  .actionsContainer {
    align-self: stretch;
    flex-grow: 0;
    padding: 6.7px 10px 8px;
    border-radius: 10px;
    background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);

    .actionLabel {
      font-family: Syncopate;
      font-size: 10px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: 0.4px;
      text-align: center;
      color: #fff;
      display: flex;
      margin-bottom: 4px;
    }
  }
}

.sectionLabel {
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  margin-top: 15px;
  text-transform: uppercase;
}

.button {
  width: 70px;
  height: 32px;
  background-color: #2a2a2a;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;

  &:hover {
    background-color: #3a3a3a;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pdfViewerContainer {
  width: 100%;
  height: 100%;
  // border-radius: 14px;
  // background: url(../../../../assets/New-images/UploadField.svg) no-repeat;
  // background-size: cover;
  // background-position: bottom right;

  .pdfInnerContainer {
    height: calc(100% - 80px);

    .pdfContainer {
      width: 100%;
      height: 100%;
    }
  }

  .noteBg {
    width: 100%;
    height: 37px;
    padding: 9px 16px;
    background-color: #5f3bd7;
    display: flex;
    font-family: Inter;
    font-size: 16.1px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: -0.48px;
    text-align: left;
    color: #fff;

    .watchDemo {
      margin-left: auto;
      font-style: italic;
    }

  }


  /* Toolbar styles */
  .toolbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0px;
    background-color: #fff;
    position: relative;
    z-index: 9999;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .toolbar {
      flex-direction: column;
      align-items: flex-start;
    }

    button,
    select {
      margin-bottom: 10px;
      width: 100%;
    }
  }

  .toolbarMain {
    display: flex;
    width: 100%;
    background-color: #fff;
  }

  .toolbarTitleContainer {
    display: inline-flex;
  }

  .toolbarBtnContainer {
    display: flex;
    flex-direction: column;
    width: 100%;

    .toolbarLeftSection {
      display: flex;
      width: 100%;
      height: 61px;
      padding: 14px 13px 14px 10px;
      box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.48);
      background-color: #191a20;

      .boxTypeButtons {
        display: flex;
        width: 100%;
        column-gap: 8px;
        row-gap: 4px;
        flex: 0 300px;

        // button {
        //   &:nth-child(1) {
        //     width: 51px;
        //   }

        //   &:nth-child(2) {
        //     width: 85px;
        //   }

        //   &:nth-child(3) {
        //     width: 137px;
        //   }

        //   &:nth-child(4) {
        //     width: 51px;
        //   }

        //   &:nth-child(5) {
        //     width: 85px;
        //   }

        //   &:nth-child(6) {
        //     width: 60px;
        //   }

        //   &:nth-child(7) {
        //     width: 70px;
        //   }
        // }
      }

      .btnCreateCustom {
        width: 79px;
        height: 100%;
        padding: 7px 6px 5px 6px;
        border-radius: 4px;
        background-color: #c3c4ca;
        font-family: Syncopate;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.2;
        letter-spacing: -0.12px;
        text-align: center;
        color: #0f0f14;

        &:hover,
        &.createCustomeActive {
          background-color: #0f0f14;
          color: #fff;
          font-weight: bold;
        }
      }
    }



  }

  .rotatePdfControl {
    margin-right: 8px;

    .rotateRight,
    .rotateLeft {
      svg {
        path {
          fill: #0f0f14
        }
      }
    }


    .rotateLeft {
      transform: rotateY(180deg);
    }
  }

  .toolbarBtmSection {
    display: flex;
    align-items: center;
    padding: 16px;

    .pdfControlBg {
      display: flex;
      column-gap: 4px;

      button {
        opacity: 0.5;
      }
    }

    .orientationSliderBg {
      width: 340px;
      height: 28px;
      flex-grow: 0;
      margin: 0 49.5px 0px 26px;
      padding: 6px 16px 6px 6px;
      border-radius: 500px;
      display: flex;
      align-items: center;
      background-color: #dbdcde;
    }

    .zoomControls {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-left: auto;
      width: 101px;
      height: 28px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 2px;
      margin: 0 0 0 12px;
      padding: 0 4px;
      opacity: 0.5;
      border-radius: 5000px;
      background-color: #dbdcde;

      .zoomPercentage {
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: -0.16px;
        text-align: center;
        color: #0f0f14;
        min-width: 42px;
      }

      button {
        background-color: #dbdcde;
        display: flex;
        align-items: center;
        border-radius: 50%;
        border: 0px;
        overflow: hidden;
        svg{
          width: 23px;
          height: 23px;
           path{
            fill: #dbdcde;
            &:nth-child(2){
              fill: #0f0f14;
            }
          }
        }
      }

    }


  }


  .custom-column-creator-container {
    position: relative;
    z-index: 1000;
  }

  .boxTypeButton {
    padding: 10px 12px 8px;
    border-radius: 500px;
    background-color: #222329;
    text-transform: uppercase;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: -0.56px;
    text-align: center;
    color: #9b9eac;
    white-space: nowrap;

  }

  .boxTypeButton:hover:not(:disabled) {
    opacity: 0.8;
  }

  .boxTypeButton.selected {
    font-weight: bold;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
  }

  .boxTypeButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .create-custom-button {
    padding: 6px 12px;
    border: 2px solid #000;
    border-radius: 4px;
    background-color: #000;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: bold;
  }

  .create-custom-button:hover:not(:disabled) {
    opacity: 0.8;
  }

  .create-custom-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

}


.filterBtn {
  width: 70px;
  min-height: 30px;
  padding: 3px 0px;
  flex-grow: 0;
  margin: 0px 0 1px;
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.25);
  font-family: Syncopate;
  font-size: 10px;
  line-height: normal;
  letter-spacing: -0.5px;
  color: #8c8b99;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.1s;
  margin-top: 2px;
  text-transform: uppercase;

  &:hover {
    background-image: url(../../../../assets/New-images/ActiveButtonBG.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    color: rgba(255, 255, 255, 0.8);
    background-color: transparent;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.3);
  }

  &:focus-visible {
    background-image: url(../../../../assets/New-images/ActiveButtonBG.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    color: rgba(255, 255, 255, 0.8);
    background-color: transparent;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.3);
  }

  &[disabled] {
    &:hover {
      background-color: rgba(0, 0, 0, 0.25);
      background-image: unset;
      border: 0px;
      color: #8c8b99;
    }
  }

  &:last-child {
    margin-bottom: 0px;
  }

  &.activeBtn {
    background-image: url(../../../../assets/New-images/ActiveButtonBG.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    color: rgba(255, 255, 255, 0.8);
    background-color: transparent;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.3);
  }

}

.pdfNavigator {
  align-self: stretch;
  flex-grow: 0;
  width: 194px;
  height: 100%;
  margin: 0 auto;
  .pdfNavigatorHeaderBTn{
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    button{
      width: 100%;
  height: 36px;
  padding: 8px 20px 8px 20px;
  border-radius: 500px;
  background-color: #222329;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #9b9eac;

    }
  }

  .pdfNavigatorPages {
    max-height: 100%;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .pageNumber {
      position: absolute;
      font-family: Syncopate;
      font-size: 40px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 1.6px;
      text-align: left;
      color: rgba(0, 0, 0, 0.38);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-width: 33px;
      text-align: center;
    }
  }

  .pdfNavigatorHeader {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.4px;
    text-align: center;
    color: #fff;
    margin-bottom: 9px;
  }

  .pdfNavigatorFooter {
    position: absolute;
    bottom: 32px;
    right: 24px;

    button {
      width: 90px;
      height: 60px;
      flex-grow: 0;
      padding: 15px 7px;
      border-radius: 10px;
      background-image: linear-gradient(to right, #0f0f14 -12%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
      font-family: Syncopate;
      font-size: 14px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: -0.7px;
      text-align: center;
      color: #8c8b99;
    }
  }
}


.scrollableContainer {
  overflow: auto;
  width: 100%;
  height: calc(100vh - 190px);
  background-color: #fff;


  .pdfPageContainer {
    width: 100%;
  }

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 50px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.magnifyGlass {
  position: absolute;
  top: 17px;
  right: 10px;

  h4 {
    font-family: Inter;
    font-size: 12.7px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.51px;
    text-align: center;
    color: #0f0f14;
  }

  p {
    font-family: Inter;
    font-size: 10px;
    font-weight: 200;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #0f0f14;
    padding-top: 10px;
  }
}

.sliderContainer {
  align-self: stretch;
  flex-grow: 0;
  padding: 6.7px 10px 8px;
  border-radius: 10px;
  background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);

  .sliderLabel {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.4px;
    text-align: center;
    color: #fff;
    display: flex;
    margin-bottom: 8px;
    text-transform: uppercase;
  }

  .sliderWrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.25);
    outline: none;
    -webkit-appearance: none;
    appearance: none;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #8c8b99;
      cursor: pointer;
      transition: all 0.1s;

      &:hover {
        background: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
      }
    }

    &::-moz-range-thumb {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #8c8b99;
      cursor: pointer;
      border: none;
      transition: all 0.1s;

      &:hover {
        background: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
      }
    }

    &:focus {
      &::-webkit-slider-thumb {
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
      }

      &::-moz-range-thumb {
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
      }
    }
  }

  .sliderValue {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    color: #8c8b99;
    text-align: center;
    min-width: 20px;
    margin-top: 3px;
  }
}

.navigatorBackground {
  // background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
  padding: 8px 4px 8px 10px;
  height: 100%;
}

.agGridHeader{
   width: 100%;
  height: 37px;
  padding: 7px 4px 6px 8px;
  background-color: #226f44;
  span{
     font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.48px;
  text-align: left;
  color: #fff;
  }
}

.btnExtract{
  position: absolute;
  right:8px;
   bottom: 11px;
   width: 155px;
  height: 45px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 15px 32px 12px;
  opacity: 0.27;
  border-radius: 16px;
  background-color: #5f3bd7;
   font-family: Syncopate;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.29;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
}