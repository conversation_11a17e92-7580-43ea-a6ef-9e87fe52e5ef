import React, { useEffect, useRef, useState } from 'react'
import styles from './TabContent.module.scss'
import { clsx } from 'clsx'
import { ReactComponent as IconUpload } from '../../../../assets/New-images/icon-upload.svg';
import { useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { v4 as uuidv4 } from 'uuid';
import { commomKeys, defaultResaleCertificateLine, prefixUrl, reactQueryKeys } from 'src/renderer2/common';
import axios from 'axios';
import { CustomMenu } from '../../CustomMenu';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { resaleCertSchema } from '../schemas/resaleCert';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { ReactComponent as DeleteIcon } from '../../../../assets/New-images/New-Image-latest/delete-outlined-cert.svg';
import { useQueryClient } from '@tanstack/react-query';
import { Dialog, Tooltip } from '@mui/material';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { InteractiveStateSelector, MultiStateSelector } from '../components/StateSelector';
import { CommonTooltip } from 'src/renderer2/component/Tooltip/tooltip';


const ExpirationMenuPropsBottom = {
    classes: {
        paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown),
        list: styles.muiMenuList,
        select: styles.selectClassName,
    },
    anchorOrigin: {
        vertical: 27,
        horizontal: "left"
    },
    transformOrigin: {
        vertical: "top",
        horizontal: "left"
    },
}


const ResaleCertTab = () => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
        getFieldState,
    } = useForm({
        resolver: yupResolver(resaleCertSchema),
        mode: 'onBlur',
    });

    const [isInputFocused, setIsInputFocused] = useState<any>({
        addNewCert: false,
    });
    const [resaleCertificateUrl, setResaleCertificateUrl] = useState("")
    const { userData, setShowLoader } = useGlobalStore()
    const [resaleCertFile, setResaleCertFile] = useState(null)
    const resaleCertFileRef = useRef<any>(null)
    const [states, setStates] = useState<any[]>([])
    const [resaleExpiration, setResaleExpiration] = useState([]);
    const { referenceData } = useGlobalStore()
    const buyerSetting = useBuyerSettingStore((state) => state.buyerSetting)
    const [resaleCertificateList, setResaleCertificateList] = useState<any[]>([])
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();
    const queryClient = useQueryClient();
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
    const [payloadData, setPayloadData] = useState('');
    const resaleCertRef = useRef<any>(null)
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();

    useEffect(() => {
        if (buyerSetting?.resale_certificate) {
            let resaleCertificateList = [...defaultResaleCertificateLine];
            const resaleCertificate = buyerSetting.resale_certificate;
            if (resaleCertificate.length !== 0) {
                resaleCertificate.forEach((data: any, i: any) => {
                    data.uploadCertProgress = false;
                    resaleCertificateList[i] = data;
                });
            }
            setResaleCertificateList(resaleCertificateList);
        }
    }, [buyerSetting])

    useEffect(() => {
        if (referenceData) {
            setStates(referenceData.ref_states);
            let expiresData: any[] = [];
            referenceData?.ref_resale_cert_expiration.map((expiration: any) => {
                const expireData = {
                    title: expiration.expiration_display_string,
                    value: expiration.expiration_value
                }
                return expiresData = [...expiresData, expireData];
            })
            setResaleExpiration(expiresData);
        }
    }, [referenceData]);

    const resaleCertEditHandler = () => {
        resaleCertFileRef?.current?.click();
    }

    const uploadResaleCertFile = async (e: any) => {
        const file = e.target.files[0]
        setValue("resaleCertFile", file)

        if (file) {
            // setValue(`resaleCertificateList.${i}.state_id`, '');
            // setValue(`resaleCertificateList.${i}.expiration_date`, '');
            // setValue(`resaleCertificateList.${i}.status`, null);
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.resaleCertPrefix + '-' + uuidv4() + '.' + ext;

            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let setCertUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
                .then(response => {
                    const signedUrl = response.data.data;
                    axios.put(signedUrl, file)
                        .then(async (response) => {
                            if (response.status === 200) {
                                setValue("resaleCertFileUrl", setCertUrl)
                                // showCommonDialog(commomKeys.uploadSuccessful, buyerSettingConst.uploadCertDialogContent, commomKeys.actionStatus.success, resetDialogStore, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }])
                            }
                        })
                        .catch(error => {
                            console.error(error)
                        }
                        );
                })
                .catch(error => {
                    console.error(error)
                }
                );

        }
    }

    const handleSaveResaleCert = async () => {
        setShowLoader(true)
        let payload = [];
        // if (resaleCertificateList.length > 0) {
        //     resaleCertificateList.forEach((certificate: any) => {
        //         if (certificate.cerificate_url_s3) {
        //             payload.push({
        //                 cerificate_url_s3: certificate.cerificate_url_s3,
        //                 expiration_date: certificate.expiration_date,
        //                 file_name: certificate.file_name,
        //                 id: certificate.id,
        //                 state_id: certificate.state_id,
        //                 status: certificate.status,
        //             })
        //         }
        //     });
        // }
        payload.push({
            cerificate_url_s3: watch("resaleCertFileUrl"),
            expiration_date: watch("expiration_date"),
            file_name: watch("resaleCertFile")?.name,
            state_id: watch("state_list"),
            status: null,
        })
        try {
            await saveUserSettings({route: 'user/buyer/settings/resale-certificate', data: payload})
            reset()
            queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
        } catch (err) {
            console.error(err)
        } finally {
            setShowLoader(false)
        }
    }

    const openDeletePopup = (cert_id, index) => {
        setPayloadData({ cert_id, index });
        setOpenDeleteConfirmation(true)
    }

    const deleteCerti = () => {
        setShowLoader(true)
        const payload = {
            data: {
                cert_id: payloadData.cert_id,
            },
        };

        setOpenDeleteConfirmation(false)
        axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`, payload)
            .then(response => {
                if (response.data.data.error_message) {
                    showCommonDialog(null, response.data.data.error_message, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                }
                else {
                    queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
                }

            })
            .catch(error => {
                console.error('Error deleting file:', error);
            }).finally(() => {
                setShowLoader(false)
            })
    };

    const handleDeleteShipment = () => {
        deleteCerti()
    }
    console.log("watch", watch())
    return (
        <div className={clsx(styles.tabContent, styles.userTabContent)} ref={resaleCertRef}>

            <div className={styles.formContainer}>
                {/* USER TYPE */}
                <div className={clsx(styles.formGroupInput, styles.addNewCert)}>
                    <span className={clsx(styles.col1,styles.addNewCertLabel)}>
                        <label
                            className={clsx(isInputFocused.userType && styles.focusLbl)}
                            htmlFor='userType'
                        >
                            ADD NEW CERTIFICATE
                        </label>
                        {isValid && <button className={styles.saveBtn} onClick={handleSaveResaleCert}>SAVE TO LIST</button>}
                    </span>
                    <span className={styles.col1} >
                        {watch("resaleCertFile")  ?
                            <div className={styles.addNewCertContainer}>
                                <div className={styles.fileName}>
                                    <span>
                                        {watch("resaleCertFile")?.name}
                                    </span>
                                    <span className={styles.uploadIcon}>
                                        <IconUpload />
                                    </span>
                                </div>
                                <div className={styles.selectUploadCertDropdownMain}>
                                  <span>
                                    {/* <CustomMenu
                                        control={control}
                                        name={`state_id`}
                                        placeholder={''}
                                        IconComponent={DropdownIcon}
                                        MenuProps={{
                                            classes: {
                                                paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown),
                                                list: styles.muiMenuList,
                                                select: styles.selectClassName,
                                            },
                                            anchorOrigin: {
                                                vertical: 27,
                                                horizontal: "left"
                                            },
                                            transformOrigin: {
                                                vertical: "top",
                                                horizontal: "left"
                                            },
                                        }}
                                        items={states.map((x: any) => ({ title: x.code, value: x.id }))}
                                        className={clsx('selectUploadCertDropdown', errors?.state_id && styles.borderOfError)}

                                    /> */}
                                    <Controller
                                        name="state_list"
                                        control={control}
                                        render={({ field }) => {
                                            // Convert state IDs to state codes for display
                                            const stateCodes = (field.value || []).map((stateId: any) => {
                                                const state = states.find((s: any) => s.id === stateId);
                                                return state?.code;
                                            }).filter(Boolean);

                                            return (
                                                <MultiStateSelector
                                                    states={states.map((state: any) => ({ state_code: state.code }))}
                                                    value={stateCodes}
                                                    onChange={(stateCodes) => {
                                                        // Convert state codes to state IDs for form storage
                                                        const stateIds = stateCodes.map(stateCode => {
                                                            const state = states.find((s: any) => s.code === stateCode);
                                                            return state?.id;
                                                        }).filter(Boolean);
                                                        field.onChange(stateIds);
                                                    }}
                                                    onBlur={field.onBlur}
                                                    error={!!errors?.state_list}
                                                    placeholder="Type to filter and select multiple states..."
                                                />
                                            );
                                        }}
                                    />
                                </span>
                                <span>
                                    <CustomMenu
                                        control={control}
                                        name={`expiration_date`}
                                        placeholder={''}
                                        IconComponent={DropdownIcon}
                                        MenuProps={ExpirationMenuPropsBottom}
                                        items={resaleExpiration}
                                        className={clsx('selectUploadCertDropdown', errors?.expiration_date && styles.borderOfError)}
                                    />
                                </span>
                                </div>
                               

                            </div>
                            :
                            <label>
                                <button onClick={resaleCertEditHandler} className={styles.uploadCert}><span>Upload</span>
                                    <span className={styles.uploadIcon}>
                                        <IconUpload />
                                    </span>
                                </button>
                            </label>
                        }
                        <input className={styles.uploadFileInput} {...register(`resaleCertFile`)} type='file' onChange={(e) => { uploadResaleCertFile(e); register(`resaleCertFile`).onChange(e) }} ref={resaleCertFileRef} />

                    </span>
                </div>

                {/* Certificate Table */}

                <div className={clsx(styles.formGroupInput,styles.bdrBtm0, styles.addNewCert)}>
                    <span className={styles.col1}>
                        <label
                            className={clsx(isInputFocused.userType && styles.focusLbl)}
                            htmlFor='userType'
                        >
                            YOUR RESALE
                            CERTificates
                        </label>
                    </span>
                    <span className={styles.col1} >
                        <div className={styles.certificateTable}>
                            {/* Table Header */}
                            <div className={styles.tableHeader}>
                                <div className={styles.headerCell}></div>
                                <div className={styles.headerCell}>VIEW CERT</div>
                                <div className={styles.headerCell}>STATE</div>
                                <div className={styles.headerCell}>EXPIRATION</div>
                                <div className={styles.headerCell}>STATUS</div>
                            </div>

                            {/* Table Body */}
                            <div className={styles.tableBody}>
                                {resaleCertificateList.map((certificate, index) => {
                                    return (
                                        <div key={index} className={styles.tableRow}>
                                            {/* VIEW CERT column */}
                                            <div className={styles.tableCell}>
                                                <div className={styles.viewCertCell}>
                                                    {certificate?.id && !!certificate.is_deletable && certificate?.status !== 'Pending' &&
                                                        <button onClick={() => openDeletePopup(certificate?.id, index)} className={styles.deleteCertBtn}><DeleteIcon /></button>
                                                    }
                                                </div>
                                            </div>
                                            <div className={styles.tableCell}>
                                                {certificate?.cerificate_url_s3 &&
                                                    <div className={styles.viewCertCell}>
                                                        <a href={certificate?.cerificate_url_s3} className={styles.viewCert}>View Cert</a>
                                                    </div>
                                                }
                                            </div>

                                            {/* STATE column */}
                                            <div className={styles.tableCell}>
                                                <div className={styles.stateBadge}>
                                                    {
                                                        certificate?.state_code?.length > 1 ? (
                                                            <>
                                                                <Tooltip
                                                                    title={certificate?.state_code?.join(', ') || ''}
                                                                    placement="bottom"
                                                                    arrow
                                                                    classes={{
                                                                        popper: 'multiCertTooltipPopper',
                                                                        tooltip: 'multiCertTooltip',
                                                                        arrow:'arrowTooltip'
                                                                    }}
                                                                >
                                                                    <span className={clsx(styles.mulitBtn)}>Multi</span>
                                                                </Tooltip>
                                                            </>
                                                        ) : certificate?.state_code
                                                    }
                                                </div>
                                            </div>

                                            {/* EXPIRATION column */}
                                            <div className={styles.tableCell}>
                                                <div className={styles.expirationBadge}>
                                                    {resaleExpiration.find(exp => exp.value === certificate.expiration_date)?.title || certificate.expiration_date}
                                                </div>
                                            </div>

                                            {/* STATUS column */}
                                            <div className={styles.tableCell}>
                                                <span className={clsx(
                                                    styles.statusText,
                                                    certificate.status === 'Approved' && styles.statusApproved,
                                                    certificate.status === 'Pending' && styles.statusPending,
                                                    certificate.status === 'Rejected' && styles.statusRejected
                                                )}>
                                                    {certificate.status}
                                                </span>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    </span>
                </div>
            </div>
            <Dialog
                open={openDeleteConfirmation}
                transitionDuration={200}
                hideBackdrop
                classes={{
                    root: styles.ErrorDialog,
                    paper: styles.dialogContent
                }}
                container={resaleCertRef.current}
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
            >
                <div>
                    <p>Are you sure you want to delete ?</p>
                    <div className={styles.deleteBtnSection}>
                        <button className={styles.submitBtn} onClick={handleDeleteShipment}>Yes</button>
                        <button className={styles.submitBtn} onClick={() => setOpenDeleteConfirmation(false)}>No</button>
                    </div>
                </div>
            </Dialog>
        </div>
    )
}

export default ResaleCertTab
