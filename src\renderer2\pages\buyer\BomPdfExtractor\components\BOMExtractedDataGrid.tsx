import React, { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import type { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'; // Import all modules
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";
import styles from "../styles/BomExtractor.module.scss";
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { parseSteel } from '@bryzos/steel-search-lib';
import * as XLSX from 'xlsx';

const INITIAL_COLUMNS: ColDef[] = [];

// Default columns to show when no data is present
const DEFAULT_COLUMNS: ColDef[] = [
  { field: "description", headerName: "Description", editable: true, flex: 2 },
  { field: "quantity", headerName: "Quantity", editable: true, flex: 1 },
  { field: "length", headerName: "Length", editable: true, flex: 1 },
  { field: "specification", headerName: "Specification", editable: true, flex: 2 },
  { field: "ProductDescription", headerName: "Matched Product", editable: false, flex: 2 }
];

// Number of empty rows to show when no data is present
const EMPTY_ROWS_COUNT = 15;

const BOMExtractedDataGrid: React.FC = () => {
  const gridRef = useRef<AgGridReact>(null);
  const [gridColumnDefs, setGridColumnDefs] = useState<ColDef[]>(INITIAL_COLUMNS);
  const [rowData, setRowData] = useState<any[]>([]);
  const {columnDef, gridRowData, productSearcher, setFinalExtractedData, setDoSubmit} = useBomPdfExtractorStore();
  const {productData} = useGlobalStore();
  
  useEffect(()=>{
    setGridColumnDefs(prev=>columnDef.map(item=>({...item, onCellValueChanged: onCellValueChanged })));
  },[columnDef]);

  const onCellValueChanged = (params)=>{
    const searchCriteria = [
      params.data.description || '',
      params.data.length || '',
      params.data.specification || '',
      params.data.grade || ''
    ].filter(Boolean).join(' ');

    const splitObj = parseSteel(searchCriteria);
    const splitString = [
      splitObj.shape || '',
      splitObj.dims || '',
      splitObj.length || '',
      splitObj.grade || ''
    ].filter(Boolean).join(' ');
    if(!splitObj.shape && !splitObj.dims && !splitObj.length && !splitObj.grade){
      // bad match set the productIds to empty array
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
      return;
    }
    const newResult = productSearcher.search(splitObj, true);
    if(newResult?.results.length === 1){
      params.data.productIds = [newResult.results[0].Product_ID];
      params.data.ProductDescription = getProductDescription(newResult.results[0].Product_ID);
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    }else{
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    }
  }

  const getProductDescription = (id) => {
    if(!productData && !id) return '';
    const product = productData.find(item=>item.Product_ID === id);
    if(product){
      return product.UI_Description;
    }

    return ''
  }

  // Create empty row data for when no data is present
  const createEmptyRows = (count: number) => {
    return Array.from({ length: count }, (_, index) => ({
      id: `empty-${index}`,
      description: '',
      quantity: '',
      length: '',
      specification: '',
      ProductDescription: '',
      productIds: []
    }));
  };

  useEffect(()=>{
    if(gridRowData.length === 0) {
      // Show default columns and empty rows when no data is present
      const columnsWithHandlers = DEFAULT_COLUMNS.map(col => ({
        ...col,
        onCellValueChanged: col.editable ? onCellValueChanged : undefined
      }));
      setGridColumnDefs(columnsWithHandlers);
      setRowData(createEmptyRows(EMPTY_ROWS_COUNT));
      return;
    }

    const tempRowData = gridRowData.map((row: any)=>{
      return {...row, ProductDescription:row.productIds.length === 1?getProductDescription(row.productIds[0]):''};
    });
    //setGridColumnDefs([...columnDef, { field: "ProductDescription", headerName: "Matched Product" }]);
    setGridColumnDefs((prev)=>{
      const temp = columnDef.map((item: any)=>({...item, onCellValueChanged: onCellValueChanged }))
      return [...temp, { field: "ProductDescription", headerName: "Matched Product" }];
    });
    setRowData([...tempRowData]);
  },[gridRowData]);

  const exportToExcel = () => {
    const exportData: any[] = [];
    gridRef.current?.api.forEachNode(({data}) => {
      const temp = gridColumnDefs.reduce((acc: any, col) => {
          if (col.field) {
            acc[col.field] = data[col.field];
          }
          return acc;
        }, {})
        exportData.push(temp);
    });
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, "grid-data.xlsx");

  };

  const getPricingPage = () => {
    console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>', gridRowData);
    console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>', rowData);
    setFinalExtractedData(()=>rowData.map((item: any)=>({...item})));
    setDoSubmit(true);
  };

  // Get header text based on whether data is present
  const getHeaderText = () => {
    if (gridRowData.length === 0) {
      return "Enter your BOM data manually or extract from PDF above";
    }
    return "Edit extracted data as needed in each cell.";
  };


  return (
    <div style={{width:'100%', height:'100%'}}>
      <div className={styles.agGridHeader}>
        <span>{getHeaderText()}</span>
        <button onClick={exportToExcel}>EXPORT</button>
      </div>
      <div className="ag-theme-alpine" style={{ height: "100%", width: "100%" }}>
       <AgGridReact
          ref={gridRef}
          columnDefs={gridColumnDefs}
          rowData={rowData}
          domLayout="autoHeight"
          headerHeight={61}   // Set header height in pixels
          rowHeight={40}      // Set row height in pixels
          suppressNoRowsOverlay={true}
          getRowStyle={(params) => {
            // Style empty rows differently
            const isEmpty = params.data?.id?.startsWith('empty-');
            const rowIndex = params.node.rowIndex ?? 0;
            return {
              backgroundColor: rowIndex % 2 === 0 ? '#ffffff' : '#f5f5f5',
              opacity: isEmpty ? 0.7 : 1,
              borderBottom: '1px solid #e0e0e0',
            };
          }}
        />
      </div>

      <button style={{    position: 'fixed',
        bottom: 60,
        right: 50,
        background: '#65ef6a',
        borderRadius: 20,
        height: 40,
        width: 200}} onClick={getPricingPage} >GET PRICING</button>
    </div>
  );
};

export default BOMExtractedDataGrid;
