import React, { useEffect, useState, useMemo } from 'react';
import styles from './NotificationsTab.module.scss';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { generateNotificationsSchema, NotificationsFormData } from '../schemas/notificationsSchema';
import clsx from 'clsx';
import { Tooltip, Fade } from '@mui/material';

export const mockNotificationsGetApiData: any = [
  {
    category: 'userAccount',
    title: 'USER ACCOUNT',
    items: [
      {
        event: 'credit_line_updates',
        title: 'CREDIT LINE UPDATES',
        tooltipText: 'Stay informed about changes to your available credit or credit line status.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'resale_cert_reminders',
        title: 'RESALE CERT REMINDERS',
        tooltipText: 'Get reminders when your resale certificate is about to expire or needs updating.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'password_updates',
        title: 'PASSWORD UPDATES',
        tooltipText: 'Get notified when your password has been changed or needs updating.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'subscription_reminders',
        title: 'SUBSCRIPTION REMINDERS',
        tooltipText: 'Get reminders about your subscription status and renewals.',
        value: { text: false, email: false, desktop: false }
      },
    ]
  },
  {
    category: 'orderUpdates',
    title: 'ORDER UPDATES',
    items: [
      {
        event: 'order_confirmations',
        title: 'ORDER CONFIRMATIONS',
        tooltipText: 'Get notified when your order has been successfully placed.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'order_changes',
        title: 'ORDER CHANGES',
        tooltipText: 'Get notified when there are changes to your existing orders.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'order_cancelations',
        title: 'ORDER CANCELATIONS',
        tooltipText: 'Get notified when your orders are cancelled.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'new_invoices',
        title: 'NEW INVOICES',
        tooltipText: 'Get notified when new invoices are available.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'new_account_statements',
        title: 'NEW ACCOUNT STATEMENTS',
        tooltipText: 'Get notified when new account statements are available.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'payment_receipts',
        title: 'PAYMENT RECEIPTS',
        tooltipText: 'Get notified when payment receipts are generated.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'vendor_chat_messaging',
        title: 'VENDOR CHAT MESSAGING',
        tooltipText: 'Get notified when you receive new messages from vendors.',
        value: { text: true, email: false, desktop: false }
      },
      {
        event: 'purchase_order_drafts',
        title: 'PURCHASE ORDER DRAFTS',
        tooltipText: 'Get notified when purchase order drafts are available.',
        value: { text: false, email: false, desktop: false }
      },
    ]
  },
  {
    category: 'appFeatures',
    title: 'APP FEATURES',
    items: [
      {
        event: 'app_updates',
        title: 'APP UPDATES',
        tooltipText: 'Stay informed about new features, improvements, or changes to the app.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'pricing_updates',
        title: 'PRICING UPDATES',
        tooltipText: 'Get notified about pricing changes and updates.',
        value: { text: true, email: false, desktop: false }
      },
      {
        event: 'product_updates',
        title: 'PRODUCT UPDATES',
        tooltipText: 'Get notified about new products or product updates.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'new_video_content',
        title: 'NEW VIDEO CONTENT',
        tooltipText: 'Get notified when new video content is available.',
        value: { text: false, email: false, desktop: false }
      },
      {
        event: 'feedback_requests',
        title: 'FEEDBACK REQUESTS',
        tooltipText: 'Get notified when feedback is requested.',
        value: { text: false, email: false, desktop: false }
      },
    ]
  }
];

// Custom notification checkbox component
const NotificationCheckbox: React.FC<{
  type: 'text' | 'email' | 'desktop';
  name: string;
  control: any;
}> = ({ type, name, control }) => {
  const label = type === 'text' ? 'T' : type === 'email' ? 'E' : 'D';

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => (
        <div
          className={clsx(
            styles.notificationCheckbox,
            {
              [styles.textType]: type === 'text',
              [styles.emailType]: type === 'email',
              [styles.desktopType]: type === 'desktop',
              [styles.checked]: value
            }
          )}
          onClick={() => onChange(!value)}
        >
          {label}
        </div>
      )}
    />
  );
};

const NotificationsTab: React.FC<{
  setActiveTab: any;
  setSaveFunctions: any;
}> = ({
  setActiveTab,
  setSaveFunctions,
}) => {
    const [apiData, setApiData] = useState<any>(mockNotificationsGetApiData);
    const [schema, setSchema] = useState(generateNotificationsSchema(mockNotificationsGetApiData));
    


    const { control, watch, handleSubmit, reset, setValue, formState: { errors, dirtyFields, isDirty, isValid, isSubmitting } } = useForm<NotificationsFormData>({
      resolver: yupResolver(schema),
      mode: "onBlur"
    });

    const isButtonDisabled = !isValid || !isDirty || isSubmitting;

    useEffect(() => {
      setSaveFunctions({
        onSave: () => handleSubmit(onSubmit)(),
        onSaveAndNext: () => handleSubmit(onSubmit)(),
        onSaveAndExit: () => handleSubmit(onSubmit)(),
        isDisabled: isButtonDisabled,
      });
    }, [isButtonDisabled, handleSubmit]);

    // Update schema and form when API data changes
    useEffect(() => {
      if(apiData) {
        const newSchema = generateNotificationsSchema(apiData);
        setSchema(newSchema);
        
        // Set form values from API data
        apiData.forEach((category: any) => {
          category.items.forEach((item: any) => {
            if (item.value.hasOwnProperty('text')) {
              setValue(`notifications.${category.category}.${item.event}.text`, item.value.text);
            }
            if (item.value.hasOwnProperty('email')) {
              setValue(`notifications.${category.category}.${item.event}.email`, item.value.email);
            }
            if (item.value.hasOwnProperty('desktop')) {
              setValue(`notifications.${category.category}.${item.event}.desktop`, item.value.desktop);
            }
          });
        });
      }
    }, [apiData, setValue]);

    const onSubmit = (data: any) => {
      console.log('Notification Settings Payload:', data);
      //save api call
      
      // Reset form to clear dirty state after successful submission
      reset(data);
    };


    return (
      <div className={styles.notificationMainContainer}>
      <div className={styles.notificationHeader}>
            Some notifications are required and unable to opt-out. Select the notifications you would
            like to receive and how you would like receive them: by
            <span className={styles.methodText}> Text &nbsp;<span className={clsx(styles.methodIcon, styles.textIcon)}>T</span>&nbsp;</span>,
            <span className={styles.methodText}> Email &nbsp;<span className={clsx(styles.methodIcon, styles.emailIcon)}>E</span>&nbsp;</span> or
            <span className={styles.methodText}> Desktop &nbsp;<span className={clsx(styles.methodIcon, styles.desktopIcon)}>D</span>&nbsp;</span>.
      </div>
      <div className={styles.notificationContainer}>
      {apiData.map((category) => (
        <div className={styles.notificationSection}>
          <div className={styles.notificationSectionTitle}>
            {category.title}
          </div>
          <div className={styles.notificationSectionContent}>
            {category.items.map((item) => (
              <div className={styles.notificationItem}>
                {item.title}
                <div className={styles.notificationToggle}>
                        {item.value.hasOwnProperty('text') && (
                          <NotificationCheckbox
                            type="text"
                            name={`notifications.${category.category}.${item.event}.text`}
                            control={control}
                          />
                        )}
                        {item.value.hasOwnProperty('email') && (
                          <NotificationCheckbox
                            type="email"
                            name={`notifications.${category.category}.${item.event}.email`}
                            control={control}
                          />
                        )}
                        {item.value.hasOwnProperty('desktop') && (
                          <NotificationCheckbox
                            type="desktop"
                            name={`notifications.${category.category}.${item.event}.desktop`}
                            control={control}
                          />
                        )}
                      </div>
                </div>
            ))}
          </div>
        </div>
      ))}
      </div>
    </div>
    );
  };

export default NotificationsTab; 