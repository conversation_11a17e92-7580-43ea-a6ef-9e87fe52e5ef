import { create } from 'zustand';

interface AppState {
  mainWindowWidth: number | null;
  setMainWindowWidth: (mainWindowWidth: number | null) => void;
  screenWidth: number;
  screenHeight: number;
  setScreenWidth: (screenWidth: number) => void;
  setScreenHeight: (screenHeight: number) => void;
}

const commonStore = {
    mainWindowWidth: null,
    screenWidth: 1440,
    screenHeight: 1024,
}
  
  
  export const useAppStore = create<AppState>((set, get) => ({
    ...commonStore,
    setMainWindowWidth: (mainWindowWidth: number | null) => set({ mainWindowWidth }),
    setScreenWidth: (screenWidth: number) => set({ screenWidth }),
    setScreenHeight: (screenHeight: number) => set({ screenHeight }),
    resetAppStore: () => set(() => ({
      ...commonStore
    })),
  }));
    