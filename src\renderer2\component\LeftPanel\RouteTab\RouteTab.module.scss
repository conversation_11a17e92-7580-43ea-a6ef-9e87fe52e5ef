.navTab{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    &:hover{
        overflow: visible;
    }
    .routingPanel{
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-top: 12px;
    }
    .logout{
        height: 3.63%;
        .logoutButton{
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.15;
            letter-spacing: normal;
            text-align: left;
            color: #71737f;
            padding: 8px;
            width: 100%;
            text-align: center;
        }
    }
    .sideBarButton{
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 1.68px;
        text-align: center;
        color: #9b9eac;
        // padding: 14.29% 14.29% 0% 14.29%;
        // aspect-ratio: 1.17;
        // height: 5.32%;
        height: 48px;
        width: 100%;
        position: relative;
        z-index: 1;
        
    }
    .routeOptions{
        &:hover{
            .routeBtnHover, .optionHoverBg{
                // width: 400%;
                opacity: 1;
            }
        }
    }
    .routeOptions{
        
        .routeBtnHover, .optionHoverBg{
            font-size: 16px;
            position: absolute;
            top: 16px;
            // width: 400%;
            // height: 100%;
            left: 46px;
            text-align: left;
            opacity: 0;
            transition: opacity 0.75s ease;
        }
        .pricingHover{
            width: 320px;
        }
        .quotingHover{
            width: 156px;
        }
        .purchasingHover{
            width: 190px;
        }
        .orderHover{
            width: 296px;
        }

    }
    .pricing:hover{
        color: #32ff6c;
        .instantPriceIcon1{
            display: none;
        }
        .instantPriceIcon2{
            display: block;
        }
    }
    .quoting:hover{
        color: #32ccff;
    }
    .purchasing:hover{
        color: #ffe352;
    }
    .order:hover{
        color: #ff8c4c;
    }
    .optionHoverBg{
        z-index: -1;
        left: 8px !important;
        bottom: 4px;
        top: 4px !important;
        box-shadow: 9px 4px 18.6px 0 #000;
        background-color: #191a20;
        border-radius: 2rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
}
.instantPriceIcon{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .instantPriceIcon1{
        display: block;
    }
    .instantPriceIcon2{
        display: none;
    }
    &:hover{
        .instantPriceIcon1{
            display: none;
        }
        .instantPriceIcon2{
            display: block;
        }
    }
}