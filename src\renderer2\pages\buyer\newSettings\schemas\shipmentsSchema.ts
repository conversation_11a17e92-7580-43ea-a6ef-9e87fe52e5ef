import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const shipmentsSchema = yup.object({
  id: yup.string().optional(),
  locationNickName: yup.string().required("Required"),
  locationAddress: yup.object().shape({
    line1: yup.string().required('Line 1 is required'),
    line2: yup.string().optional(),
    city: yup.string().required('City is required'),
    state: yup.string().required('State is required'),
    stateCode: yup.string().required('State code is required'),
    zip: yup.string()
  }).required('Location address is required'),
  dates: yup.array().of(yup.mixed()).optional(),
  deliveryApptRequired: yup.boolean().default(false),
  deliveryContact: yup.object().shape({
    firstName: yup.string().required("Required"),
    lastName: yup.string().required("Required"),
    phone:yup.string().min(12, 'Phone number is not valid').required('Phone number is required'),
    email:yup.string().trim().required("Required").test('valid-emails', 'Enter valid email', value => {
      if (!value) return true;
      const emails = value.split(',');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    }),
  }).required("Required"),
  shippingDocsEmail:  yup.string().trim().required("Required").test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  isDefault: yup.boolean().default(false),
});

export type ShipmentsFormData = yup.InferType<typeof shipmentsSchema>; 