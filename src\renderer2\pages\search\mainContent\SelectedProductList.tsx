import { ReactComponent as CenterArrowIcon } from '../../../assets/New-images/New-Image-latest/no-result-arrow-icon.svg';
import { ReactComponent as DropDownArrowIcon } from '../../../assets/New-images/StateIconDropDpown.svg';
import { ReactComponent as DeleteArrowIcon } from '../../../assets/New-images/New-Image-latest/delete-outlined.svg';
import clsx from 'clsx';
import ProductDescriptionAndPricing from './ProductDescriptionAndPricing';
import styles from '../home.module.scss'
import { shareEmailTypes } from 'src/renderer2/common';
import ShareEmailWindow from 'src/renderer2/component/ShareEmailWindow/ShareEmailWindow';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { useState, useEffect, useRef } from 'react';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import { exportToExcel, formatOrderSizeToDisplayText, updateSelectedPriceSearchData, newPriceFormatter } from 'src/renderer2/helper';
import { priceUnits, useGlobalStore, useSearchStore } from '@bryzos/giss-ui-library';
import { RowData } from '../../exporttToExcelUtils';

const SelectedProductList = () => {
    const { filterShortListedSearchProductsData, searchByProductResult } = useSearchStore();
    const focusSingleProduct = useSearchStore(state => state.focusSingleProduct);
    const selectedProductsData = useSearchStore(state => state.selectedProductsData);
    const setSelectedProductsData = useSearchStore(state => state.setSelectedProductsData);
    const setFocusSingleProduct = useSearchStore(state => state.setFocusSingleProduct);
    const setSaveFeedbackMap = useSearchStore(state => state.setSaveFeedbackMap);
    const saveFeedbackMap = useSearchStore(state => state.saveFeedbackMap);
    const shortListedSearchProductsData = useSearchStore(state => state.shortListedSearchProductsData);
    const setShortListedSearchProductsData = useSearchStore(state => state.setShortListedSearchProductsData);
    const setFilterShortListedSearchProductsData = useSearchStore(state => state.setFilterShortListedSearchProductsData);
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType  } = useRightWindowStore();
    const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
    const selectedDomesticOption = useSearchStore(state => state.selectedDomesticOption);
    const [isExportDropdownOpen, setIsExportDropdownOpen] = useState(false);
    const exportContainerRef = useRef<HTMLDivElement>(null);
    const referenceData: any = useGlobalStore(state => state.referenceData);
    const selectedPriceUnit = useSearchStore(state => state.selectedPriceUnit);
    const orderSizeList = referenceData?.ref_weight_price_brackets || [];
    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (exportContainerRef.current && !exportContainerRef.current.contains(event.target as Node)) {
                setIsExportDropdownOpen(false);
            }
        };

        if (isExportDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isExportDropdownOpen]);
    
    const handleDeleteLines = () => {
        const spreadSelectedSearchProductsData = [...shortListedSearchProductsData];
        const filterShortListedSearchProductsData = spreadSelectedSearchProductsData.filter((productData) => {
            if (!focusSingleProduct[productData.id]) return true;
            setSaveFeedbackMap({ ...saveFeedbackMap, [productData.id]: undefined });
            return false;
        });
        setShortListedSearchProductsData(filterShortListedSearchProductsData);
        updateSelectedPriceSearchData(filterShortListedSearchProductsData);
        setFocusSingleProduct({});
        setSelectedProductsData([]);
    }
    
    const handleDeleteAll = () => {
        setShortListedSearchProductsData([]);
        setFilterShortListedSearchProductsData([]);
        setSelectedProductsData([]);
        setSaveFeedbackMap({});
        setFocusSingleProduct({});
        updateSelectedPriceSearchData([]);
    }
    
    const handleSharePrice = () => {
        console.log('Share Pricing Data ', selectedProductsData)
        setShareEmailWindowProps({isSharePrice: true});
        setLoadComponent(<ShareEmailWindow />)
        setShareEmailType(shareEmailTypes.sharePrice);
    }

    const handleExportToPDF = () => {
        console.log('Export to PDF', selectedSavedSearch);
        setIsExportDropdownOpen(false);
        const formattedData = formatSavedSearchDataForExport(selectedSavedSearch);
        console.log('Formatted Data', formattedData);
    }

    const handleExportToExcel = () => {
        console.log('Export to Excel', selectedSavedSearch);
        setIsExportDropdownOpen(false);
        const formattedData = formatSavedSearchDataForExport(selectedSavedSearch);
        console.log('Formatted Data', formattedData);
        const rows:RowData[] = [{
                row: [
                    { cellText: 'Title'},
                    { cellText: 'Zip Code'},
                    { cellText: 'Order Size (LB)'},
                    { cellText: 'Pricing Unit'},
                    { cellText: 'Domestic Required', style: { b:false}},
                    { cellText: 'Search Time'},
                ],
                style: { 
                    b: true,
                    backgroundColor: 'FFFFEB9C',
                    border:true
                }
            },
            {
                row: [
                    { cellText: formattedData.title},
                    { cellText: formattedData.zipcode},
                    { cellText: formattedData.order_size},
                    { cellText: formattedData.price_unit},
                    { cellText: formattedData.domestic_material_only},
                    { cellText: formattedData.search_date_time},
                ],
                style: { 
                    border:true
                }
            },
            {
                row: [
                    { cellText: ' '},//Empty row
                ]
            },
            {
                row: [
                    { cellText: 'Description'},
                    { cellText: 'Price'},
                    { cellText: 'Pricing Unit'},
                ],
                style: { 
                    b: true,
                    backgroundColor: 'FFFFEB9C',
                    border:true
                }
            }
        ]

        formattedData.products.forEach(p => {
            rows.push({
                row: [
                    { cellText: p.product_description},
                    { cellText: p.price},
                    { cellText: p.price_unit},
                ],
                style: { 
                    border:true
                }
            })
        })

        // const metadataSheet = [
        //     ["Title", "Zip Code", "Order Size (LB)", "Pricing Unit", "Domestic Required", "Search Time"],
        //     [formattedData.title, formattedData.zipcode, formattedData.order_size, formattedData.price_unit, formattedData.domestic_material_only, formattedData.search_date_time],
        // ];

        // const productSheet = [
        // ["Description", "Price", "Pricing Unit"],
        // ...formattedData.products.map(p => [p.product_description, p.price, p.price_unit])
        // ];

        // const wb = XLSX.utils.book_new();
        // XLSX.utils.book_append_sheet(wb, XLSX.utils.aoa_to_sheet([...metadataSheet, ...productSheet]), "Search");
        // //XLSX.utils.book_append_sheet(wb, XLSX.utils.aoa_to_sheet(productSheet), "Products");

        // XLSX.writeFile(wb, `${sanitizeFilename(formattedData.title)}.xlsx`);
        //exportToExcel(formattedData);
        exportToExcel(rows, formattedData.title);
    }

    // async function exportToExcel(formattedData: any) {
    //     const workbook = new ExcelJS.Workbook();
    //     const sheet = workbook.addWorksheet('Search');
    //     const borderStyle = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

    //     // Metadata headers (bold)
    //     const metadataHeaders = ["Title", "Zip Code", "Order Size (LB)", "Pricing Unit", "Domestic Required", "Search Time"];
    //     const metadataValues = [formattedData.title, formattedData.zipcode, formattedData.order_size, formattedData.price_unit, formattedData.domestic_material_only, formattedData.search_date_time];

    //     sheet.addRow(metadataHeaders).eachCell(cell => {
    //         cell.font = { bold: true };
    //         cell.border = borderStyle;
    //     });

    //     sheet.addRow(metadataValues).eachCell(cell => {
    //         cell.border = borderStyle;
    //     });;

    //     // Empty row between sections (optional)
    //     sheet.addRow([]);

    //     // Product sheet headers (bold)
    //     const productHeaders = ["Description", "Price", "Pricing Unit"];
    //     sheet.addRow(productHeaders).eachCell(cell => {
    //         cell.font = { bold: true };
    //         cell.border = borderStyle;
    //     });

    //     // Product rows
    //     formattedData.products.forEach(p => {
    //         sheet.addRow([p.product_description, p.price, p.price_unit]).eachCell(cell => {
    //         cell.border = borderStyle;
    //     });;
    //     });

    //     // Optional: Adjust column widths
    //     sheet.columns.forEach(col => {
    //         let maxLength = 10;
    //         col.eachCell?.(cell => {
    //         const len = cell.value?.toString().length ?? 0;
    //         if (len > maxLength) maxLength = len;
    //         });
    //         col.width = maxLength + 2;
    //     });

    //     // Create Blob and download
    //     const buffer = await workbook.xlsx.writeBuffer();
    //     const blob = new Blob([buffer], {
    //         type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    //     });
    //     saveAs(blob, `${sanitizeFilename(formattedData.title)}.xlsx`);
    //     }

    function sanitizeFilename(name:string) {
        // Replace disallowed characters: \ / : * ? " < > | and control characters (ASCII < 32)
        const illegalCharsRegex = /[\\\/:\*\?"<>\|\x00-\x1F]/g;

        // Replace disallowed Unicode characters for macOS (e.g., colon)
        const macUnsafe = /[:]/g;

        // Combine and replace with empty string, then replace spaces with underscores
        return name
            .replace(illegalCharsRegex, '')
            .replace(macUnsafe, '')
            .replace(/\s+/g, '_')
            .substring(0, 255); // Truncate to 255 characters (safe cross-platform length)
    }


    const handleAddToPO = () => {
        console.log('Add to PO', selectedSavedSearch);
    }

    const handleAddToQuote = () => {
        console.log('Add to Quote', selectedSavedSearch);
    }   

     const formatSavedSearchDataForExport = (selectedSavedSearch: any) => {
        console.log('Selected Products Data', selectedProductsData);
        const formattedData = {
            title: selectedSavedSearch?.title || 'Untitled',
            search_date_time: selectedSavedSearch?.search_date_time || '',
            zipcode: selectedSavedSearch?.zipcode || '',
            order_size: selectedSavedSearch?.order_size ? formatOrderSizeToDisplayText(orderSizeList, Number(selectedSavedSearch?.order_size)) : '-',
            price_unit: selectedPriceUnit ? selectedPriceUnit.toUpperCase() : priceUnits.cwt.toUpperCase(),
            domestic_material_only: selectedDomesticOption ? 'Yes' : 'No',
            item_count: selectedSavedSearch?.item_count || 0,
            pricing_expired: selectedSavedSearch?.pricing_expired || false,
            products: selectedProductsData.map((product: any) => {
                return {
                    product_description: product.UI_Description,
                    price: newPriceFormatter(product),
                    price_unit: selectedPriceUnit ? selectedPriceUnit.toUpperCase() : priceUnits.cwt.toUpperCase()
                }
            })
        }
        return formattedData;
     }

    return (
        <div className={styles.selectedProductWrapper}>
            <div className={styles.selectedProductHeaderContainer}>
                <div className={clsx(styles.selectedProductHeaderButtonsContainer, selectedProductsData.length > 0 && styles.enableHover)}>
                    <button className={styles.selectedProductHeaderButton} onClick={handleAddToPO} disabled={selectedProductsData.length === 0}>Add to PO</button>
                    <button className={styles.selectedProductHeaderButton} onClick={handleAddToQuote} disabled={selectedProductsData.length === 0}>Add to Quote</button>
                    <button className={styles.selectedProductHeaderButton} disabled={selectedProductsData.length === 0} onClick={handleSharePrice}>Share Pricing</button>
                    <div className={styles.exportContainer} ref={exportContainerRef}>
                        <button 
                            className={clsx(styles.selectedProductHeaderButton, styles.exportButton)}
                            onClick={() => setIsExportDropdownOpen(!isExportDropdownOpen)}
                            disabled={selectedProductsData.length === 0}
                        >
                            Export
                            <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                        </button>
                        {isExportDropdownOpen && (
                            <div className={styles.exportDropdown}>
                                <button 
                                    className={styles.exportOption}
                                    onClick={handleExportToPDF}
                                >
                                    Export to PDF
                                </button>
                                <button 
                                    className={styles.exportOption}
                                    onClick={handleExportToExcel}
                                >
                                    Export to Excel
                                </button>
                            </div>
                        )}
                    </div>
                    {/* <button className={styles.selectedProductHeaderButton}>Save</button> */}
                </div>
                {!selectedSavedSearch?.pricing_expired && 
                    <div className={styles.hoverButtonContainer}>
                        <div className={clsx(styles.buttonWrapper, selectedProductsData.length > 0 && styles.enableHover)}>
                            
                            <button className={styles.defaultButton} onClick={handleDeleteAll}>
                            <DeleteArrowIcon /> <span>{(selectedProductsData.length <= 0) ? "All" : "x"+selectedProductsData.length}</span>
                            </button>
                            <div className={styles.hoverButtons}>
                                <button className={styles.deleteSelectedBtn} onClick={handleDeleteLines}><DeleteArrowIcon /> x {selectedProductsData.length}</button>
                                <button className={styles.allBtn} onClick={handleDeleteAll}>All</button>
                            </div>
                        </div>
                    </div>
                }
            </div>
            {(selectedProductsData.length > 0) ? <div className={styles.itemCountPop}>{selectedProductsData.length} Items Selected</div> : ""}
            {(filterShortListedSearchProductsData.length === 0) &&
                <div className={styles.NoResultsToDisplay} data-hover-video-id='price-search'>
                    <CenterArrowIcon />
                    <div className={styles.NoResultsToDisplayText}>
                        <p className={styles.marginBottom10}>NO RESULTS TO DISPLAY</p>
                        <p>TYPE IN SEARCH BAR ABOVE</p>
                    </div>
                </div>
            }
            <div className={clsx(styles.selectedProductDescriptionMain, (searchByProductResult.length !== 0) && styles.searchResult)}>
                {filterShortListedSearchProductsData.map((selectedSearchProduct, i) => (
                    <ProductDescriptionAndPricing
                        key={selectedSearchProduct.id} i={i}
                        selectedSearchProduct={selectedSearchProduct}
                    />
                ))}
            </div>
        </div>
    )
}

export default SelectedProductList