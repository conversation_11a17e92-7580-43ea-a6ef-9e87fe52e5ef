import { routes } from 'src/renderer2/common';
import styles from './leftPanel.module.scss';
import ListTab from './ListTab/ListTab';
import RouteTab from './RouteTab/RouteTab';
import PdfNavigator from 'src/renderer2/pages/buyer/BomPdfExtractor/components/PdfNavigator';
import clsx from 'clsx';
import { useAppStore } from 'src/renderer2/utility/AppStore';

const LeftPanel = () => {
    const mainWindowWidth = useAppStore(state => state.mainWindowWidth);
    const screenWidth = useAppStore(state => state.screenWidth);

    return (
        <div className={clsx(styles.leftPanel, mainWindowWidth!==null && 'flexSpace')}>
            <div className={styles.routeTab} style={{width: `${screenWidth*0.0389}px`}}>
                <RouteTab />
            </div>
            {
                location.pathname !== routes.buyerSettingPage && <>
                    {location.pathname === routes.bomExtractor ? <div className={styles.pdfNavigatorMain}>
                        <PdfNavigator />
                    </div> : <div className={styles.listTab}>
                        <ListTab />
                    </div>

                    }

                </>
            }
           
        </div>
    )
}

export default LeftPanel;