import React, { useState } from 'react';
import './OrientationSlider.scss';

const OrientationSlider = ({handlePageRotation , orientation , setOrientation}:{handlePageRotation:(orientation:number)=>void, orientation:number, setOrientation:(orientation:number)=>void}) => {
  const marks = [-180, -90, 0, 90, 180];

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOrientation(parseInt(e.target.value));
     // Convert slider value to rotation: add 360 and mod 360
     const rotationValue = (parseInt(e.target.value) + 360) % 360;
     handlePageRotation(rotationValue);
 // handlePageRotation(parseInt(e.target.value));
  };

  return (
    <div className="orientation-slider-container">
      <span className="orientation-label">ORIENTATION</span>
      
      <div className="slider-wrapper">
        <div className="slider-track">
          <input
            type="range"
            min="-180"
            max="180"
            step="1"
            value={orientation}
            onChange={handleSliderChange}
            className="slider-input"
          />
          
          <div className="marks-container">
            {marks.map((mark) => (
              <div
                key={mark}
                className="mark"
                style={{
                  left: `${((mark + 180) / 360) * 100}%`
                }}
              >
                <div className="mark-line"></div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="value-display">
          <span className="value-number">{orientation}</span>
        </div>

        <span className="value-unit">DEG</span>
      </div>
    </div>
  );
};

export default OrientationSlider;
