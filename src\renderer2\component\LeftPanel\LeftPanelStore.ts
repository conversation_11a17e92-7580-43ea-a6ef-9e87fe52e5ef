// @ts-nocheck
import { localStorageKeys } from 'src/renderer2/common';
import { getLocal, setLocal } from 'src/renderer2/helper';
import { create } from 'zustand';

interface MenuState {
  openLeftPanel: boolean;
  closeWithoutAnimation: boolean;
  leftPanelData: any;
  displayLeftPanel: boolean;
  quoteList: any;
  selectedQuote: any;
  setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => void;
  resetLeftPanelStore: () => void;
  setOpenLeftPanel: (openLeftPanel: boolean) => void;
  setLeftPanelData: (leftPanelData: any) => void; 
  setDisplayLeftPanel: (displayLeftPanel: boolean) => void;
  setQuoteList: (quoteList: any) => void;
  setSelectedQuote: (selectedQuote: any) => void;
}

const commonStore = {
    openLeftPanel:false,
    closeWithoutAnimation:false,
    leftPanelData: null,
    displayLeftPanel: false,
    quoteList: [],
    selectedQuote: null,
}
  
  
  export const useLeftPanelStore = create<MenuState>((set, get) => ({
    ...commonStore,
    setOpenLeftPanel: (openLeftPanel: boolean) => set({ openLeftPanel }),
    setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => set({ closeWithoutAnimation }),
    setLeftPanelData: (leftPanelData: any) => set({ leftPanelData }),
    setDisplayLeftPanel: (displayLeftPanel: boolean) => set({ displayLeftPanel }),
    setQuoteList: (quoteList: any) => set({ quoteList }),
    setSelectedQuote: (selectedQuote: any) => set({ selectedQuote }),
    resetLeftPanelStore: () => set(state => ({
      ...commonStore
    })),
  }));
    