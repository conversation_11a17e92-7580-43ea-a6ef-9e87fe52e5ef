.headerMain {
  display: flex;
  align-items: center;

  .logoSection {
    margin-right: 30px;
    display: flex;
    position: relative;
    height: 40px;
    width: 40px;

    .bryzosLogo {
      position: absolute;
      height: 47px;
      width: 56px;
      left: -16px;
    }

    svg {
      width: 40px;
      height: 40px;
    }
  }

  .priceSearchMargin {
    margin-left: 40px;
    margin-right: 24px;
  }

  .pageName {
    font-family: Syncopate;
    font-size: 25.6px;
    font-weight: bold;
    line-height: 1.5;
    letter-spacing: -1.02px;
    text-align: left;
    color: #fff;
    display: flex;
    height: 40px;
    padding-top: 3px;

    .greenDot {
      cursor: pointer;
      width: 5px;
      height: 5px;
      background-color: #43f776;
      border-radius: 50%;
      margin: 4.2px 0px 0px 3px;
    }
  }

  .iconMain {
    display: flex;
    column-gap: 7px;
    margin-left: auto;

    .iconTopVc {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      flex-grow: 0;
      padding: 0px 5.5px 0px 5.5px;
      object-fit: contain;
      border-radius: 3px;
      // border: solid 0.5px rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.04);
      font-family: Syncopate;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.28;
      letter-spacing: -0.28px;
      text-align: center;
      color: rgba(255, 255, 255, 0.2);
      cursor: pointer;

      &:hover {
        color: #0f0f14;
        background-color: #ff6060;
        font-weight: bold;
      }

      &:focus-visible {
        color: #0f0f14;
        background-color: #ff6060;
        font-weight: bold;
      }
    }

    .iconTopInviteUser {
      width: 132px;
      height: 36px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 8px;
      padding: 2px 9px 0;
      border-radius: 3px;
      border: solid 0.5px rgba(155, 158, 172, 0.5);
      font-family: Syncopate;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.28;
      letter-spacing: -0.56px;
      text-align: center;
      color: #9b9eac;
      cursor: pointer;

      &:hover {
        background-color: rgba(255, 255, 255, 0.91);
        color: #0f0f14;
      }

      &:focus {
        outline: none;
      }

      &:focus-visible {
        background-color: rgba(255, 255, 255, 0.91);
        color: #0f0f14;
      }
    }

    .activeIconTopInviteUser {
      background-color: rgba(255, 255, 255, 0.91);
      color: #0f0f14;
    }

    // .iconDiv {
    //   display: flex;
    //   height: 32px;
    //   width: 32px;
    //   cursor: pointer;

    //   svg {
    //     height: 32px;
    //     width: 32px;
    //     border-radius: 3px;
    //   }

    //   &:hover {
    //     .iconDivImg1 {
    //       display: none;
    //     }

    //     .iconDivImg2 {
    //       display: block;
    //     }
    //   }

    //   &:focus {
    //     outline: none;
    //   }

    //   &:focus-visible {
    //     .iconDivImg1 {
    //       display: none;
    //     }

    //     .iconDivImg2 {
    //       display: block;
    //     }
    //   }

    //   .iconDivImg2 {
    //     display: none;
    //   }
    // }
  }

  .iconTopSubscribe {
    margin-top: 7px;
  }

}

.pinFocus.pinFocus {
  .iconDivImg1 {
    display: none;
  }

  .iconDivImg2.iconDivImg2 {
    display: block;
  }
}

// .subscribeTextDiv {
//   padding-right: 40px;
//   padding-top: 11px;
//   position: absolute;
//   top: 0px;
//   right: 0;
//   color: #fff;
//   display: flex;
//   flex-direction: column;
//   text-align: right;

//   .subscribeText {
//     font-family: Syncopate;
//     font-size: 12px;
//     font-weight: bold;
//     line-height: 1.15;
//     letter-spacing: 0.84px;
//     color: #fff;
//   }

//   .subscribeText1 {
//     font-family: Inter;
//     font-size: 12px;
//     font-weight: normal;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: normal;
//     letter-spacing: 0.24px;
//     text-align: right;
//     color: #43f776;
//     text-transform: uppercase;
//   }

//   .freeTrialText1 {
//     font-family: Syncopate;
//     font-size: 10px;
//     font-weight: bold;
//     line-height: 1.2;
//     text-align: center;
//     color: #32ff6c;
//   }
// }

.priceSearchSubscribeTextDiv {
  padding-right: 23px;
}

.bomBackpage {
  font-family: Syncopate;
  font-size: 10px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0.4px;
  text-align: center;
  color: #32ff6c;
  position: absolute;
  bottom: 10px;
  left: 45px;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  z-index: 9;

  svg {
    margin-right: 3px;
    margin-right: 3px;
    width: 12px;
    height: 12px;

    path {
      stroke: #32ff6c;
      stroke-opacity: 1;
    }
  }

}

.stagingEnv {
  padding: 2px 10px;
  border-radius: 5000px;
  margin-left: 10px;
  background-image: linear-gradient(to right, #00c6ff, #0072ff);
  font-family: Inter;
  font-size: 12px;
  color: #fff;
  line-height: 1.6;
  display: flex;
  align-items: center;
  font-weight: 400;
  height: 27px;
  margin-top: auto;
  margin-bottom: auto;
}

.qaEnv {
  padding: 2px 10px;
  border-radius: 5000px;
  margin-left: 10px;
  background-image: linear-gradient(to right, #11998E, #38EF7D);
  font-family: Inter;
  font-size: 12px;
  color: #fff;
  line-height: 1.6;
  display: flex;
  align-items: center;
  font-weight: 400;
  height: 27px;
  margin-top: auto;
  margin-bottom: auto;
}

.demoEnv {
  padding: 2px 10px;
  border-radius: 5000px;
  margin-left: 10px;
  background-image: linear-gradient(to right, #ec008c, #fc6767);
  font-family: Inter;
  font-size: 12px;
  color: #fff;
  line-height: 1.6;
  display: flex;
  align-items: center;
  font-weight: 400;
  height: 27px;
  margin-top: auto;
  margin-bottom: auto;
}

.headerStyle {
  width: 100%;
}

.subscribeBtn {
  width: 152px;
  height: 48px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 0px 16px 0 103px;
  padding: 12px 20px 10px;
  border-radius: 5000px;
  background-color: #32ff6c;
  font-family: Syncopate;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
  letter-spacing: 0.56px;
  text-align: left;
  color: #0f0f14;
  text-transform: uppercase;
}

.headerRightSection {
  display: flex;
  align-items: center;
  margin-left: auto;

  .mainSearch {
    width: 523px;
    height: 48px;
    flex-grow: 0;
    margin: 0px 16px 0;
    padding: 8px 8px 8px 16px;
    border-radius: 30px;
    background-color: rgba(255, 255, 255, 0.04);
    display: flex;
    align-items: center;
    gap: 16px;

    input {
      width: 100%;
      height: 100%;
      background-color: transparent;
      border: none;
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.64px;
      text-align: left;
      color: #fff;
      transition: all 0.1s ease-in-out;

      &::placeholder {
          color: #9b9eac;
      }

      &:focus {
        outline: none;
      }
    }
  }

  .iconDivContainer {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    margin-left: 12px;
    padding: 0;
    object-fit: contain;

    .iconDiv {
      cursor: pointer;
      &:hover {
        .iconDivImg1 {
          display: none;
        }

        .iconDivImg2 {
          display: inline-flex;
        }
      }

      &:focus {
        outline: none;
      }

      &:focus-visible {
        .iconDivImg1 {
          display: none;
        }

        .iconDivImg2 {
          display: inline-flex;
        }
      }

      .iconDivImg2 {
        display: none;
      }
    }
    .settingIconDiv{
      .iconDivImg1{
        display: none;
      }
      .iconDivImg2{
        display: inline-flex;
      }
    }
  }
}