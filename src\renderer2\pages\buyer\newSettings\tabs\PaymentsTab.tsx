import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './PaymentsTab.module.scss';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { CardCvcElement, CardExpiryElement, CardNumberElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { buyerSettingConst, ueGetBuyingPreference, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { dispatchRaygunError, formatCurrency, formatCurrencyWithComma, formatEIN, formatToTwoDecimalPlaces, removeCommaFromCurrency, trueVaultRaygunError } from 'src/renderer2/helper';
import { useFavicon } from '@mantine/hooks';
import { commomKeys, BNPLSTATUS, userRole } from 'src/renderer2/common';
import axios from 'axios';
import TrueVaultClient from 'truevault';
import { ClickAwayListener, Dialog } from '@mui/material';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';
import usePostCancelBnplRequest from 'src/renderer2/hooks/usePostCancelBnplRequest';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { paymentsSchema } from '../schemas';
import { ReactComponent as TrueVaultLogo } from '../../../../assets/New-images/New-Image-latest/payment-method-vault-logo.svg';

interface InputFocusState {
  creditLimit: boolean;
  outstanding: boolean;
  available: boolean;
  bankName: boolean;
  accountName: boolean;
  bankRoutingNumber: boolean;
  bankAccountNumber: boolean;
  billingZipCode: boolean;
  dnBNumber: boolean;
  einNumber: boolean;
  creditLine: boolean;
  requestCreditLine: boolean;
}

// Add custom styling for Stripe Elements
const stripeElementsStyle = {
  base: {
    color: '#fff',
    fontFamily: 'Inter, sans-serif',
    fontSize: '18px',
    '::placeholder': {
      color: "rgba(255, 255, 255, 0.2)",
    },
    padding: '6px 16px',
  },
  invalid: {
    color: '#ff6b6b',
    iconColor: '#ff6b6b',
  },
};


const PaymentsTab: React.FC = () => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(paymentsSchema),
    mode: 'onBlur',
  });

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    creditLimit: false,
    outstanding: false,
    available: false,
    bankName: false,
    accountName: false,
    bankRoutingNumber: false,
    bankAccountNumber: false,
    billingZipCode: false,
    dnBNumber: false,
    einNumber: false,
    creditLine: false,
    requestCreditLine: false
  });
  const [stripeError, setStripeError] = useState<{
    cardNumber: string | null,
    cardExpiry: string | null,
    cardCvv: string | null,
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvv: null,
  });
  const [cardBrand, setCardBrand] = useState<string | null>(null);

  const stripe = useStripe();
  const elements = useElements();
  const { userData, subscriptionStatus, setShowLoader, setSubscriptionStatus, referenceData }: any = useGlobalStore();
  const { setBuyerSettingInfo } = useBuyerSettingStore();
  const [net30Default, setNet30Default] = useState<string | null>(null);
  const [achDefault, setAchDefault] = useState<string | null>(null);
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvv: false
  });
  const [isCreditCardDirty, setIsCreditCardDirty] = useState<boolean>(false);
  
  const userSubscription = {
    card_number_last_four_digits: null,
    cardExpiry: null,
  }
  const [requestCreditLineDialogOpen, setRequestCreditLineDialogOpen] = useState(false);
  const {showCommonDialog, resetDialogStore}: any = useDialogStore();
  const maskedRoutingNo = getValues('refRoutingNo') ? '*'.repeat(getValues('refRoutingNo').length) : '';
  const maskedAccountNo = getValues('refAccountNo') ? '*'.repeat(getValues('refAccountNo').length) : '';
  const [showMaskedAchDetails, setShowMaskedAchDetails] = useState(true);
  const paymentCreditReq = useRef(null);
  const [editModeDesiredCreditLine, setEditModeDesiredCreditLine] = useState(false);
  const [validationInProgress, setValidationInProgress] = useState({
    submitBnpl: false,
    requestCreditLine: false,
    cancelIncreaseCreditLimitRequest: false,
    cancelBnplRequest: false,
    editBnplRequest: false
  });
  const [isBnplFilled, setIsBnplFilled] = useState<boolean>(false);
  const [isBnplApproved, setIsBnplApproved] = useState<string>('');
  const [bnplCreditStatus, setBnplCreditStatus] = useState<string>('');
  const [bnplStatus, setBnplStatus] = useState<string>(''); 
  const [isBnplEditMode, setIsBnplEditMode] = useState<boolean>(false);

  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();

  const {
    mutateAsync: cancelBnplRequest
  } = usePostCancelBnplRequest();
  
  const getBuyingPreference = ueGetBuyingPreference();
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();
  const {buyerSetting} = useBuyerSettingStore();

  const enableSaveCardInfo = useMemo(() => {
    return cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvv && !stripeError.cardNumber && !stripeError.cardExpiry && !stripeError.cardCvv && watch('billingZipCode') && !errors.billingZipCode && isCreditCardDirty;
  }, [cardComplete, stripeError, errors, watch, isCreditCardDirty]);

  useEffect(() => {
    if(buyerSetting){
      console.log('buyerSetting in payments tab', buyerSetting);
      if (buyerSetting.ach_credit) {
        setValue('bankName', buyerSetting.ach_credit.bank_name);
        setValue('routingNo', buyerSetting.ach_credit.routing_number);
        setValue('accountNo', buyerSetting.ach_credit.account_number);
      }
  
      if (buyerSetting.bnpl_settings) {
        let requestedCredit = parseFloat(
          buyerSetting.bnpl_settings.requested_credit_limit
        ).toString();
  
        if (buyerSetting.bnpl_settings.is_approved !== 0) {
          setValue('dnBNumber', buyerSetting.bnpl_settings.duns);
          setValue('einNumber', buyerSetting.bnpl_settings.ein_number);
          setValue('creditLine', requestedCredit);
        }
        setValue('requestedCreditLimit', requestedCredit);
        // setNet30ApplyStatus(true);
  
        let balanceCredit = formatCurrency(
          buyerSetting.bnpl_settings.balance_credit_limit
        );
        setValue('balanceCreditLimit', balanceCredit);
        if (buyerSetting.bnpl_settings.requested_increase_credit) {
          let requestedIncreaseCredit = formatCurrency(
            parseFloat(buyerSetting.bnpl_settings.requested_increase_credit)
          );
          setValue('requestedIncreaseCredit', requestedIncreaseCredit);
        }
        let availableBalanceCredit = formatCurrency(
          parseFloat(buyerSetting.bnpl_settings.balance_available_credit_limit)
        );
        setValue('availableBalance', availableBalanceCredit);
        let outstandingValue = '0';
        if (buyerSetting.bnpl_settings.outstanding_amount === null) {
          outstandingValue = '0';
        } else {
          outstandingValue = buyerSetting.bnpl_settings.outstanding_amount;
        }
        let outstandingBalanceAmount = formatCurrency(
          parseFloat(outstandingValue)
        );
        if(buyerSetting.bnpl_settings?.max_restricted_amount){
          setValue('max_restricted_amount', buyerSetting.bnpl_settings.max_restricted_amount ?? "0")
        }
        setValue('outstandingAmount', outstandingBalanceAmount);
        setIsBnplApproved(buyerSetting.bnpl_settings.is_approved);
        setBnplCreditStatus(buyerSetting.bnpl_settings.credit_status);
        setIsBnplFilled(true);
        setBnplStatus(buyerSetting.bnpl_settings.bnpl_status);
      } else {
        setIsBnplApproved('');
        setBnplCreditStatus('');
        setBnplStatus('');
        setIsBnplFilled(false);
      }
  
      if (buyerSetting?.credit_card) {
        setValue(
          'cardType',
          buyerSetting?.credit_card?.card_display_brand || ''
        );
        setValue(
          'cardNumberLast4Digits',
          buyerSetting?.credit_card?.card_number_last_four_digits || ''
        );
        setValue('cardExpiry', buyerSetting?.credit_card?.expiration_date || '');
        setValue('billingZipCode', buyerSetting?.credit_card?.zipcode || '');
        setValue(
          'cardEmailId',
          buyerSetting?.credit_card?.email_id || userData?.data?.email_id || ''
        );
        setValue(
          'cardFirstName',
          buyerSetting?.credit_card?.first_name ||
            userData?.data?.first_name ||
            ''
        );
        setValue(
          'cardLastName',
          buyerSetting?.credit_card?.last_name || userData?.data?.last_name || ''
        );
      }
      
    }
  }, [buyerSetting]);


  useEffect(() => {
    return () => {
      setValue('requestCreditLine', '')
    }
  }, []);



  useEffect(() => {
    if (userData?.data?.id && referenceData) {
      referenceData?.ref_pgpm_mapping.map((paymentMethod: any) => {
        if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'BALANCE') {
            setNet30Default(paymentMethod.payment_method);
        }
        if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'DEFAULT') {
            setAchDefault(paymentMethod.payment_method);
        }
    })
    if (referenceData.ref_account_routing_number[0]) {
      if (referenceData.ref_account_routing_number[0]?.account_number) {
          setValue('refAccountNo', referenceData.ref_account_routing_number[0]?.account_number);
      }
      if (referenceData.ref_account_routing_number[0]?.routing_number) {
          setValue('refRoutingNo', referenceData.ref_account_routing_number[0]?.routing_number);
      }
      if (referenceData.ref_account_routing_number[0]?.account_name) {
        setValue('accountName', referenceData.ref_account_routing_number[0]?.account_name);
      }
  }
    }
  }, [referenceData, userData]);

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    // setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));
    setIsCreditCardDirty(true);

    // Capture card brand when it's a card number element
    if (fieldName === 'cardNumber' && event.brand && event.brand !== 'unknown') {
      setCardBrand(event.brand);
      setValue('cardType', event.brand.toUpperCase());
    }

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  }, [stripeError, setValue]);

  const handleZipValidation = async (): Promise<any> => {
    if(watch('billingZipCode') && watch('billingZipCode').trim() !== '' && watch('billingZipCode').trim().length > 4){
    try {
      const res = await verifyZipCode({ zip_code: watch('billingZipCode') });
      if (res) {
        return true;
      } else {
        setError('billingZipCode', { message: 'Invalid zipcode' });
        return false;
      }
    } catch (error) {
        console.log('error', error);
        setError('billingZipCode', { message: 'Invalid zipcode' });
        return false;
      }
    }
  };

  const handleZipOnBlur = async () => {
    await handleZipValidation();
  }


  console.log("stripeError", stripeError);
  console.log("errors", errors);
  console.log("cardComplete", cardComplete);

  const handleCardSubmit = async () => {
    try {
      const isZipValid = await handleZipValidation();
      if (!stripeError.cardNumber && !stripeError.cardExpiry && !stripeError.cardCvv && isZipValid) {
        setShowLoader(true);
        if (!stripe || !elements) {
          // Stripe.js hasn't loaded yet
          setStripeError("Stripe hasn't loaded yet. Please try again.");
          setShowLoader(false);
          return;
      }
        let _paymentMethod;
        const cardElement = elements?.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvv && watch('billingZipCode')) {
          const { paymentMethod, error: paymentMethodError }: any = await stripe?.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: watch('cardEmailId'),
              name: `${watch('cardFirstName')} ${watch('cardLastName')}`,
              address: {
                country: 'US',
                postal_code: watch('billingZipCode'),
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }
        
        const payload = {
          "data": {
            "payment_method": "credit_card",
            "payment_details": {
              "zipcode": watch('billingZipCode'),
              "payment_method_id": _paymentMethod?.id ? _paymentMethod.id : undefined
            }
          }
        }
        await buyerSettingsPayment(payload);
        setIsCreditCardDirty(false);
      }
      
      let response;
      // if (userSubscription.active_customer || isUpdatePaymentModule) {
      //   response = await updateUserPayment(payload);
      //   setSubscriptionStatus(true);
      // } else {
      //   response = await saveUserSubscription(payload);
      //   if (!response || !response?.client_secret) {
      //     setStripeError('No client secret received from server');
      //     setShowLoader(false);
      //     return;
      //   }

      //   const { client_secret: clientSecret } = response;
      //   const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(clientSecret);
      //   if (confirmError) {
      //     setStripeError(confirmError.message || 'An error occurred during payment processing');
      //   } else if (paymentIntent.status === 'succeeded') {

      //   }
      // }
    } catch (error: unknown) {
      const err = error as Error;
      setStripeError(prev => ({
        ...prev,
        cardNumber: err.message || 'An error occurred during payment processing'
      }));
      showCommonDialog(
        null,
        err.message || 'An error occurred during payment processing',
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
    } finally {
      setShowLoader(false);
    }
  }

  const handleEinNoChange = (e: any) => {
    const { value } = e.target;
    setValue('einNumber', formatEIN(value));
  }

  const requestCreditLineChangeHandler = (e: any, fieldName: string) => {
    let value = sanitizeNumberInput(e.target.value);
    clearErrors(fieldName);
    
    if (!isNaN(removeCommaFromCurrency(value))) {
      setValue(fieldName, removeCommaFromCurrency(value));
    }
  }

  // Function to sanitize input - removes negative signs, spaces, and keeps only numbers
  const sanitizeNumberInput = (value: string): string => {
    // Remove negative signs, spaces, and keep only digits and decimal points
    return value.replace(/[-\s]/g, '').replace(/[^0-9.]/g, '');
  }

  const isBnplSubmitDisabled = watch('dnBNumber') && watch('creditLine') && watch('einNumber') && !validationInProgress.submitBnpl;

  const bnplSetupValidate = async () => {
    if (!watch('dnBNumber') || !watch('creditLine') || !watch('einNumber')) {
      const net30Fields = ['creditLine', 'dnBNumber', 'einNumber'];
      for (let trial in net30Fields) {
        if (watch(net30Fields[trial]) === '') {
          setError(net30Fields[trial], { message: `Net 30 Terms is not valid` });
        }
      }

    } else {
      const isValid = await trigger(['dnBNumber', 'creditLine', 'einNumber']);
      if (!isValid) {
        return;
      }
      if (+watch('creditLine') <= 0) {
        setError('creditLine', { message: `Net 30 Terms is not valid` });
        return;
      }
      if (watch('creditLine') > buyerSettingConst.buyerCreditLineLimit) {
        setError('creditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
        return
      }
      clearErrors(['dnBNumber', 'creditLine', 'einNumber']);
      console.log('isBnplEditMode', isBnplEditMode);
      if(isBnplEditMode){
        await  cancelBnplRequest()
      }
      applyNetData();
    }
  }

  const applyNetData = () => {
    setValidationInProgress({
      ...validationInProgress,
      submitBnpl: true
    });
    getTruevaultData(getValues('parentCompanyName'), userData.data.id, null, null, null, net30Default, getValues('einNumber'), getValues('dnBNumber')).then(documentIdFromTruevault => {
      const payload = {
        "data": {
          "ein_number": getValues('einNumber').slice(-2).padStart(getValues('einNumber').length, 'x'),
          "duns_number": getValues('dnBNumber'),
          "agreed_terms": true,
          "desired_credit_limit": getValues('creditLine'),
          "pgpm_mapping_id": 4,
          "reference_document_id": documentIdFromTruevault ? documentIdFromTruevault : ''
        }
      };

      axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveBuyNowPayLaterData', payload, {
        headers: {
          UserId: userData.data.id
        }
      })
        .then(res => {
          if (res.status === 200) {
            setBnplStatus('PENDING');
            setValidationInProgress({
              ...validationInProgress,
              submitBnpl: false
            });

            getBuyingPreference.mutateAsync().then(res => {
              if (res?.data?.data !== 'SUCCESS') {
                const buyerPreferenceData = res.data.data;
                setBuyerSettingInfo(buyerPreferenceData);
              }
            });
            // setSuccessAppSubmit(true)
            // setNet30ApplyStatus(true);
          } else if(res?.data?.data.error_message) {
            showCommonDialog(
              null,
              commomKeys.errorContent,
              commomKeys.actionStatus.error,
              resetDialogStore,
              [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
            );
            }
        })
        .catch(err => {
          console.error(err);
          setValidationInProgress({
            ...validationInProgress,
            submitBnpl: false
          });
          showCommonDialog(
            null,
            commomKeys.errorContent,
            commomKeys.actionStatus.error,
            resetDialogStore,
            [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
          );
          // setApiFailureDialog(true);
        });
    })

  }

  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, paymentMethod, einNumber, dnBNumber) => {
    try {
      const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
      const accessToken = res.data.data;
      let buyerPaymentData = {};
      if (paymentMethod === achDefault) {
        buyerPaymentData = {
          "document": {
            "company_name": companyName,
            "user_id": userData,
            "bank_name": bankName,
            "routing_number": routingNo,
            "account_number": accountNo,
            "payment_method": paymentMethod
          }
        }
      }
      else if (paymentMethod === net30Default) {
        buyerPaymentData = {
          "document": {
            "company_name": companyName,
            "user_id": userData,
            "dnb_number": dnBNumber,
            "net_30_ein": einNumber,
            "payment_method": paymentMethod
          }
        }
      }

      const client = new TrueVaultClient({ accessToken });

      try {
        const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_BUYER_VAULT_ID, null, buyerPaymentData);
        const documentIdFromTruevault = response.id;
        if (!documentIdFromTruevault) {
          dispatchRaygunError(new Error('TrueVault error: TruevoltObject = ' + JSON.stringify(response)), [trueVaultRaygunError]);
        }
        return documentIdFromTruevault;

      } catch (error) {
        console.error("Error creating document:", error);
        dispatchRaygunError(error, [trueVaultRaygunError]);
        setValidationInProgress({
          ...validationInProgress,
          submitBnpl: false
        });
      }

    } catch (err) {
      console.error(err)
    }

  }

  const submitRequestCreditLine = () => {
    let unFormattedCreditLineValue = removeCommaFromCurrency(getValues('requestCreditLine'))
    if (Number(unFormattedCreditLineValue) > buyerSettingConst.buyerCreditLineLimit) {
        setError('requestCreditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
        // showErrorKeyVal(['requestCreditLine'])
        return;
    }
    clearErrors();
    if (Number(unFormattedCreditLineValue) > 0) {
      setValidationInProgress({
        ...validationInProgress,
        requestCreditLine: true
      });
        const payload = {
            "data": {
                "request_increase_credit": unFormattedCreditLineValue
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/increaseCreditLimitRequest', payload)
            .then(res => {
                if (res?.data?.data?.error_message) {
                    showCommonDialog(null, res.data.data.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                } else {
                    setBnplCreditStatus('Pending Increase')
                    setValue('requestedIncreaseCredit', formatCurrency(parseFloat(unFormattedCreditLineValue)))
                    setRequestCreditLineDialogOpen(false);
                }
                setValue('requestCreditLine', '')
                setValidationInProgress({
                  ...validationInProgress,
                  requestCreditLine: false
                });
                // setRequestCreditLineAmount(false)
            })
            .catch(err => {
                console.error(err);
                setShowLoader(false);
                setValidationInProgress({
                  ...validationInProgress,
                  requestCreditLine: false
                });
                // setApiFailureDialog(true)
            }
            );
    } else {
        setError('requestCreditLine', 'field is not vaild', { shouldFocus: true });
        setError('type', 'error checking')
        setValidationInProgress({
          ...validationInProgress,
          requestCreditLine: false
        });
    }
  }

  const handleCancelRequestCreditLine = () => {
    setValidationInProgress({
      ...validationInProgress,
      cancelIncreaseCreditLimitRequest: true
    });
      axios.post(import.meta.env.VITE_API_SERVICE + `/user/cancelIncreaseCreditLimitRequest`)
          .then(res => {
              setBnplCreditStatus(null);
              if(res?.data?.data?.error_message){
                showCommonDialog(
                  null,
                  res?.data?.data?.error_message || commomKeys.errorContent,
                  commomKeys.actionStatus.error,
                  resetDialogStore,
                  [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                );
              }
              setValidationInProgress({
                ...validationInProgress,
                cancelIncreaseCreditLimitRequest: false
              });
          })
          .catch(err => {
              console.error(err);
              setShowLoader(false);
              setValidationInProgress({
                ...validationInProgress,
                cancelIncreaseCreditLimitRequest: false
              });
              showCommonDialog(
                null,
                commomKeys.errorContent,
                commomKeys.actionStatus.error,
                resetDialogStore,
                [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
              );
              // setApiFailureDialog(true)
          });
  }

  const handleRevealCashAdvance = () => {
    setShowMaskedAchDetails(false);
  }

  const handleCancelBnplRequest = () => {
    try {
      setValidationInProgress({
        ...validationInProgress,
        cancelBnplRequest: true
      });
      cancelBnplRequest().then(() => {
        setBnplStatus('');
        setValue('dnBNumber', '');
        setValue('einNumber', '');
        setValue('creditLine', '');
        setValidationInProgress({
          ...validationInProgress,
          cancelBnplRequest: false
        });
      });
    } catch (error) {
      console.error(error);
      showCommonDialog(
        null,
        error?.message || commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      setValidationInProgress({
        ...validationInProgress,
        cancelBnplRequest: false
      });
    }
  }


  const handleEditBnplRequest = () => {
    try {
      setIsBnplEditMode(true);
      setBnplStatus('');
      // setValue('dnBNumber', '');
      setValue('einNumber', '');
      // setValue('creditLine', '');
    } catch (error) {
      console.error(error);
      showCommonDialog(
        null,
        error?.message || commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
    }finally{
      setValidationInProgress({
        ...validationInProgress,
        editBnplRequest: false
      });
    }
  }

  const handleCopyInfo = () => {
    const bankName = watch('bankName');
    const routingNo = watch('refRoutingNo');
    const accountNo = watch('refAccountNo');
    const accountName = watch('accountName');

    const textToCopy = `Bank Name: ${bankName}\nAccount Name: ${accountName}\nBank Routing Number: ${routingNo} \nBank Account Number: ${accountNo}`;

    navigator.clipboard.writeText(textToCopy)
      .catch(err => {
        console.error('Failed to copy:', err);
      });
  };


  return (
    <div style={{height:'100%'}}>
      <div className={styles.paymentMethodContainer}>
        <div className={styles.paymentMethodHeader}>
          <span>Enter payment methods to be accessible when creating a purchase order.</span>
          <TrueVaultLogo />
        </div>
        <div className={styles.paymentMethodBody}>
          <div className={styles.paymentMethodContent}>
            <div className={styles.paymentMethodItemHeader}>
              <div className={styles.headerItem}>
                NET 30 TERMS
              </div>
              <div className={styles.headerItemBtn}>
                {
                  requestCreditLineDialogOpen ? <>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, errors?.requestCreditLine && styles.error)}
                        type='text'
                        mode="commaNumber"
                        register={register("requestCreditLine")}
                        placeholder='Enter Amount ($)'
                      />
                    </InputWrapper>
                    <button className={styles.headerBtn} onClick={() => submitRequestCreditLine()} disabled={validationInProgress.requestCreditLine}><span>Submit</span></button>
                  </>
                    :
                    (

                      bnplStatus && bnplStatus !== 'REJECTED' ? (
                        bnplStatus === 'PENDING' ? <>
                          <span className={clsx(styles.headerItemStatus, styles.headerItemStatusPending)}>PENDING REVIEW</span>
                          <button className={styles.headerBtn} disabled={validationInProgress.editBnplRequest} onClick={handleEditBnplRequest}><span>Edit</span></button>
                          <button className={styles.headerBtn} disabled={validationInProgress.cancelBnplRequest} onClick={handleCancelBnplRequest}><span>Cancel Request</span></button>
                        </>
                          : bnplCreditStatus && bnplCreditStatus === 'Pending Increase' ? <>
                            <span className={styles.headerItemStatus}>CL INCREASE PENDING REVIEW</span>
                            <button className={styles.headerBtn} onClick={handleCancelRequestCreditLine} disabled={validationInProgress.cancelIncreaseCreditLimitRequest}><span>Cancel Request</span></button>
                          </>

                            :
                            <>
                              <span className={clsx(styles.headerItemStatus, (bnplStatus === BNPLSTATUS.ON_HOLD || bnplStatus === BNPLSTATUS.RESTRICTED || bnplStatus === BNPLSTATUS.REJECTED) && styles.onCreditHold)}>
                                {bnplStatus === BNPLSTATUS.ON_HOLD ? 'ON CREDIT HOLD' : bnplStatus === BNPLSTATUS.RESTRICTED ? `RESTRICTED ${formatCurrency(watch('max_restricted_amount'))}` :
                                  bnplStatus === BNPLSTATUS.REJECTED ? 'REJECTED' :
                                    'APPROVED'}
                              </span>
                              <button className={styles.headerBtn} onClick={() => setRequestCreditLineDialogOpen(true)} disabled={bnplStatus === BNPLSTATUS.ON_HOLD || bnplStatus === BNPLSTATUS.RESTRICTED}><span>Request CL Increase</span></button>
                            </>
                      )
                        :
                        <>
                          <button className={styles.headerBtn} disabled={!isBnplSubmitDisabled} onClick={() => bnplSetupValidate()}><span>Apply for Terms</span></button>
                        </>
                    )
                }
              </div>
            </div>
            {
              bnplStatus && bnplStatus !== 'PENDING' && bnplStatus !== 'REJECTED' ? (
                <>
                  <div className={styles.paymentMethodContain}>
                    <div className={styles.paymethodContainTitle} > Creadit Limit</div>
                    <div className={styles.paymethodContainValue}>
                      <span className={styles.inputCreateAccount}>
                        {watch('balanceCreditLimit')}
                      </span>
                    </div>
                  </div>
                  <div className={styles.paymentMethodContain}>
                    <div className={styles.paymethodContainTitle} > OUTSTANDING</div>
                    <div className={styles.paymethodContainValue}>
                      <span className={styles.inputCreateAccount}>
                        {watch('outstandingAmount')}
                      </span>
                    </div>
                  </div>
                  <div className={styles.paymentMethodContain}>
                    <div className={styles.paymethodContainTitle} > AVAILABLE</div>
                    <div className={styles.paymethodContainValue}>
                      <span className={styles.inputCreateAccount}>
                        {watch('availableBalance')}
                      </span>
                    </div>
                  </div>

                </>
              ) :
                bnplStatus === 'PENDING' ?
                  (
                    <>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} > D&B NUMBER</div>
                        <div className={styles.paymethodContainValue}>
                          <span className={styles.inputCreateAccount}>
                            {watch('dnBNumber')}
                          </span>
                        </div>
                      </div>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} > COMPANY EIN</div>
                        <div className={styles.paymethodContainValue}>
                          <span className={styles.inputCreateAccount}>
                            {watch('einNumber')}
                          </span>
                        </div>
                      </div>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} > COMPANY EIN</div>
                        <div className={styles.paymethodContainValue}>
                          <span className={styles.inputCreateAccount}>
                            $ {watch("creditLine") ? formatToTwoDecimalPlaces(watch("creditLine")) : ''}
                          </span>
                        </div>
                      </div>
                    </>
                  ) :

                  (
                    <>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} htmlFor="dnBNumber"> D&B NUMBER</div>
                        <div className={styles.paymethodContainValue}>
                          <InputWrapper>
                            <CustomTextField
                              className={clsx(styles.inputCreateAccount, errors?.dnBNumber && styles.error)}
                              type='text'
                              mode="wholeNumber"
                              register={register("dnBNumber")}
                              placeholder=''
                              maxLength={9}
                              onChange={(e: any) => {
                                register("dnBNumber").onChange(e)
                                const dnb = e.target.value.replace(/\D/g, '');
                                setValue('dnBNumber', dnb);
                              }}
                            />
                          </InputWrapper>
                        </div>
                      </div>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} htmlFor="einNumber"> COMPANY EIN</div>
                        <div className={styles.paymethodContainValue}>
                          <InputWrapper>
                            <CustomTextField
                              className={clsx(styles.inputCreateAccount, errors?.einNumber && styles.error)}
                              type='text'
                              register={register("einNumber")}
                              placeholder=''
                              maxLength={10}
                              onChange={(e) => {
                                register("einNumber").onChange(e)
                                handleEinNoChange(e)
                              }}
                              onKeyDown={(e) => {
                                if (e.key === 'Tab' && !e.shiftKey) {
                                  e.preventDefault();
                                  setTimeout(() => {
                                    setEditModeDesiredCreditLine(true)
                                    const creditLineInput = document.querySelector(`input[id="desired-credit-line"]`);
                                    if (creditLineInput instanceof HTMLElement) {
                                      creditLineInput.focus();
                                    }
                                  }, 100);
                                }
                              }}
                            />
                          </InputWrapper>
                        </div>
                      </div>
                      <div className={styles.paymentMethodContain}>
                        <div className={styles.paymethodContainTitle} htmlFor="creditLine"> D&B NUMBER</div>
                        <div className={styles.paymethodContainValue}>
                          {editModeDesiredCreditLine ? (
                            <ClickAwayListener
                              onClickAway={() => setEditModeDesiredCreditLine(false)}
                            >
                              <input
                                id='desired-credit-line'
                                type="string"
                                className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)}
                                autoFocus={true}
                                value={
                                  (+watch("creditLine")) ?
                                    (formatCurrencyWithComma(watch("creditLine")) ?? "") :
                                    (watch("creditLine") ?? "")
                                }
                                onChange={(e) => {
                                  requestCreditLineChangeHandler(e, "creditLine")
                                }}
                                onBlur={() => {
                                  setEditModeDesiredCreditLine(false)
                                }}
                              />

                            </ClickAwayListener>
                          ) : (
                            <span className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)} onClick={() => setEditModeDesiredCreditLine(true)}>
                              $ {watch("creditLine") ? formatToTwoDecimalPlaces(watch("creditLine")) : ''}
                            </span>
                          )}
                        </div>
                      </div>
                    </>
                  )
            }
          </div>
          <div className={styles.paymentMethodContent}>
            <div className={styles.paymentMethodItemHeader}>
              <div className={styles.headerItem}>
                CREDIT/DEBIT CARD
              </div>
              <div className={styles.headerItemBtn}>
                <button className={clsx(styles.headerBtn, !enableSaveCardInfo && styles.disabled)} htmlFor="bankName" disabled={!enableSaveCardInfo} onClick={handleCardSubmit}>
                  <span>Save Card Info</span>
                </button>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="bankName"> CARD TYPE</div>
              <div className={styles.paymethodContainValue}>
              <span className={styles.col1}>
                <span className={clsx(styles.inputCreateAccount)}>{watch('cardType') || ''}</span>
              </span>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="accountName"> CARD NUMBER</div>
              <div className={styles.paymethodContainValue}>
                <div className={styles.stripeElement}>

                  <CardNumberElement options={{
                    style: stripeElementsStyle,
                    placeholder: watch('cardNumberLast4Digits') ? `**** **** **** ${watch('cardNumberLast4Digits')}` : '0000 0000 0000 0000'

                  }}
                    onChange={(e) => handleCardChange(e, 'cardNumber')}
                    // onBlur={() => handleCardSubmit()}
                  />
                </div>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="bankRoutingNumber"> EXPIRATION / CVV</div>
              <div className={styles.stripePaymentGrid}>
              <div className={styles.stripeElement}>
                  <CardExpiryElement options={{
                    style: stripeElementsStyle,
                    placeholder: watch('cardExpiry') ? `${watch('cardExpiry')}` : 'MM / YY'
                  }}
                    onChange={(e) => handleCardChange(e, 'cardExpiry')}
                    // onBlur={() => handleCardSubmit()}
                  />
                </div>
                <div className={styles.stripeElement}>
                  <CardCvcElement options={{
                    style: stripeElementsStyle,
                    placeholder: 'CVV'
                  }}
                    onChange={(e) => handleCardChange(e, 'cardCvv')}
                    // onBlur={() => handleCardSubmit()}
                  />
                </div>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="billingZipCode"> CARD NUMBER</div>
              <div className={styles.paymethodContainValue}>
              <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.billingZipCode && styles.error)}
                    type='text'
                    register={register("billingZipCode")}
                    placeholder='XXXXX'
                    maxLength={5}
                    mode="wholeNumber"
                    // onBlur={() => handleZipOnBlur()}
                  />
                </InputWrapper>
              </div>
            </div>
          </div>
          <div className={styles.paymentMethodContent}>
            <div className={styles.paymentMethodItemHeader}>
              <div className={styles.headerItem}>
              CASH IN ADVANCE
              </div>
              <div className={styles.headerItemBtn}>
              <button className={styles.headerBtn} onClick={handleRevealCashAdvance}><span >Click to Reveal</span></button>
                <button className={clsx(styles.headerBtn)}  onClick={handleCopyInfo}>
                  <span>Copy Info</span>
                </button>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="bankName">  BANK NAME</div>
              <div className={styles.paymethodContainValue}>
              <span className={styles.inputCreateAccount}>
                  {watch('bankName')}
                </span>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="accountName"> ACCOUNT NAME</div>
              <div className={styles.paymethodContainValue}>
              <span className={styles.inputCreateAccount}>
                    {watch('accountName')}
                  </span>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="bankRoutingNumber"> BANK ROUTING NUMBER</div>
              <div className={styles.paymethodContainValue}>
              <span className={styles.inputCreateAccount}>
                    { showMaskedAchDetails ? maskedRoutingNo : watch('refRoutingNo')}
                  </span>
              </div>
            </div>
            <div className={styles.paymentMethodContain}>
              <div className={styles.paymethodContainTitle} htmlFor="bankAccountNumber"> BANK ACCOUNT NUMBER</div>
              <div className={styles.paymethodContainValue}>
              <span className={styles.inputCreateAccount}>
                    { showMaskedAchDetails ? maskedAccountNo : watch('refAccountNo')}
                  </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentsTab; 