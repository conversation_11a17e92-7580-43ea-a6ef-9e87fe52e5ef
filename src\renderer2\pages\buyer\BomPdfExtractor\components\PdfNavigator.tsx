import React, { useState, useEffect, useRef } from "react";
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";
import styles from "../styles/BomExtractor.module.scss";

const PdfNavigator = () => {
  const [pdfDoc, setPdfDoc] = useState(null);
  const [overlayRect, setOverlayRect] = useState(null);
  const canvasRefs = useRef([]);
  const overlayCanvasRefs = useRef([]);
  const containerRef = useRef(null);
  const scrollableContainerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const {allBoxes, pdfjs, sourceCanvasRefs, boxDrawing, setDoNext, scrollViewPort, setUpdatedScrollViewPort, pageRotations} = useBomPdfExtractorStore();

  // 1. Load the PDF document once pdfjs and fileUrl are available
  useEffect(() => {
    if (!pdfjs) return;
    setPdfDoc(pdfjs)
  }, [pdfjs]);

  const desiredWidth = 160;

  const renderPage = async (pageNum) => {
    const page = await pdfDoc.getPage(pageNum)
    const tempViewport = page.getViewport({ scale: 1, rotation: pageRotations + page.rotate});
    const scale = desiredWidth / tempViewport.width;

    const viewport = page.getViewport({ scale, rotation: pageRotations + page.rotate });
    const canvas = canvasRefs.current[pageNum - 1];
    canvas.width = viewport.width;
    canvas.height = viewport.height;
    const context = canvas.getContext("2d");
    await page.render({ canvasContext: context, viewport });
  };


  useEffect(() => {
    if(!sourceCanvasRefs) return;
    const desiredWidth = 160;

    sourceCanvasRefs.forEach(async(sourceCanvasRef,index)=>{
        if(!sourceCanvasRef||!canvasRefs.current[index]||!overlayCanvasRefs.current[index]) return;
        const src = sourceCanvasRef;
        const scale = desiredWidth / src.width;
        overlayCanvasRefs.current[index].width = src.width * scale;
        overlayCanvasRefs.current[index].height = src.height * scale;
        const ctx = overlayCanvasRefs.current[index].getContext('2d');
        ctx.clearRect(0, 0, canvasRefs.current[index].width, canvasRefs.current[index].height);
        ctx.drawImage(src, 0, 0, src.width, src.height, 0, 0, src.width * scale, src.height * scale);
    })


  }, [allBoxes, boxDrawing]);


  // 2. Once pdfDoc is loaded, render each page into its canvas
  useEffect(() => {
    if (!pdfDoc) return;

    for (let i = 1; i <= pdfDoc.numPages; i++) {
      renderPage(i);
    }
  }, [pdfDoc, pageRotations]);
  function clamp(value, min, max) {  
    return Math.min(Math.max(value, min), max);  
  }  

  function checkOverlayVisibility(overlayRect, container) {
    const { scrollLeft, scrollTop, clientWidth, clientHeight } = container;
    const viewRect = {
      x: scrollLeft,
      y: scrollTop,
      width: clientWidth,
      height: clientHeight
    };
  
    // compute how far overlay’s left/top edges are past view’s left/top
    const deltaLeft   = overlayRect.x - viewRect.x;
    const deltaTop    = overlayRect.y - viewRect.y;
  
    // compute how far overlay’s right/bottom edges are past view’s right/bottom
    const deltaRight  = (overlayRect.x + overlayRect.width)  - (viewRect.x + viewRect.width);
    const deltaBottom = (overlayRect.y + overlayRect.height) - (viewRect.y + viewRect.height);
  
    return {
      inView: deltaLeft >= 0 && deltaTop >= 0 && deltaRight <= 0 && deltaBottom <= 0,
      outLeft: Math.max(0, -deltaLeft),
      outTop: Math.max(0, -deltaTop),
      outRight: Math.max(0, deltaRight),
      outBottom: Math.max(0, deltaBottom)
    };
  }



  useEffect(() => {
    if(!scrollViewPort || !containerRef.current || !scrollableContainerRef.current) return;
    const {width, height} = scrollableContainerRef?.current.getBoundingClientRect();
    const xMin = 0;
    const yMin = 0;
    const xMax = 1 - scrollViewPort.width;
    const yMax = 1 - scrollViewPort.height;
    const calculateOverlayRect = {
      x:clamp(scrollViewPort.x,xMin, xMax)*width,
      y:clamp(scrollViewPort.y,yMin, yMax)*height,
      width:scrollViewPort.width*width,
      height:scrollViewPort.height*height,
    }
    setOverlayRect(calculateOverlayRect);
    if(!overlayRect || !containerRef.current || !scrollableContainerRef.current) return;
    const container = containerRef.current;
    const visibility = checkOverlayVisibility(overlayRect, container);
    if(!visibility.inView) {
      container.scrollTop += visibility.outBottom - visibility.outTop;
    }

  }, [scrollViewPort]);

  const handleMouseDown = (e) => {
    if (!containerRef.current || !overlayRect || !scrollableContainerRef.current) return;
    
    // Get mouse position relative to container
    const rect = scrollableContainerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Get container dimensions
    const containerWidth = rect.width;
    const containerHeight = rect.height;
    
    // Calculate new position with overlay centered on mouse
    const newX = mouseX - (overlayRect.width / 2);
    const newY = mouseY - (overlayRect.height / 2);
    
    // Clamp to container bounds
    const clampedX = clamp(newX, 0, containerWidth - overlayRect.width);
    const clampedY = clamp(newY, 0, containerHeight - overlayRect.height);
    
    // Update overlay position immediately
    setOverlayRect({
      ...overlayRect,
      x: clampedX,
      y: clampedY
    });
    
    // Update scroll viewport in the store
    if (containerWidth > 0 && containerHeight > 0) {
      setUpdatedScrollViewPort({
        ...scrollViewPort,
        x: clampedX / containerWidth,
        y: clampedY / containerHeight,
      });
    }
    
    // Set drag offset to center of overlay
    setDragOffset({
      x: 0,
      y: 0
    });

    if (containerWidth > 0 && containerHeight > 0) {
      setUpdatedScrollViewPort({
        ...scrollViewPort,
        x: clampedX / containerWidth,
        y: clampedY / containerHeight,
      });
    }
    
    setIsDragging(true);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !containerRef.current || !overlayRect || !scrollableContainerRef.current) return;
    
    // Get container dimensions
    const containerRect = scrollableContainerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;
    
    // Get mouse position relative to container
    const mouseX = e.clientX - containerRect.left;
    const mouseY = e.clientY - containerRect.top;
    
    // Calculate new center position (accounting for the initial offset)
    const centerX = mouseX - dragOffset.x;
    const centerY = mouseY - dragOffset.y;
    
    // Calculate new top-left position
    const newX = centerX - (overlayRect.width / 2);
    const newY = centerY - (overlayRect.height / 2);
    
    // Clamp to container bounds
    const clampedX = clamp(newX, 0, containerWidth - overlayRect.width);
    const clampedY = clamp(newY, 0, containerHeight - overlayRect.height);
    
    // Update overlay position
    setOverlayRect({
      ...overlayRect,
      x: clampedX,
      y: clampedY
    });
    
    // Update scroll viewport in the store
    if (containerWidth > 0 && containerHeight > 0) {
      setUpdatedScrollViewPort({
        ...scrollViewPort,
        x: clampedX / containerWidth,
        y: clampedY / containerHeight,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add document-level event listeners to handle mouse up outside the container
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => {
        setIsDragging(false);
      };
      
      const handleGlobalMouseMove = (e) => {
        handleMouseMove(e);
      };
      
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('mousemove', handleGlobalMouseMove);
      
      return () => {
        document.removeEventListener('mouseup', handleGlobalMouseUp);
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }
  }, [isDragging]);

  return (
    pdfDoc &&
    <div className={styles.pdfNavigator}>
      <div className={styles.pdfNavigatorHeaderBTn}>
        <button>Back</button>
        <button>Upload BOM</button>
        <button>Clear All Boxes</button>
      </div>
      <div 
        className={styles.navigatorBackground}
      >
       
        <div 
          className={styles.pdfNavigatorPages} 
          style={{position:'relative', width:desiredWidth}}
          ref={containerRef}
        >
        <div
          style={{
            position: "absolute",
            top: overlayRect?.y,
            left: overlayRect?.x,
            width: overlayRect?.width,
            height: overlayRect?.height,
            border: "1px dashed #000",
            zIndex: 1000,
            pointerEvents: "none",
            background: "rgba(0, 0, 0, 0.2)",
          }}
        ></div>
        <div 
          style={{width:desiredWidth, cursor: isDragging ? 'grabbing' : 'grab'}}
          ref={scrollableContainerRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
        >
        {Array.from({ length: pdfDoc.numPages }, (_, index) => (
          <div key={index} style={{ position: "relative", margin: '0 0 2px 0'}}>
            <span className={styles.pageNumber}>{index + 1}</span>
            <canvas
              key={`page_${index + 1}`}
              ref={(el) => (canvasRefs.current[index] = el)}
              style={{ display: "block" }}
            />
            <canvas
              ref={(el) => (overlayCanvasRefs.current[index] = el)}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                zIndex: 1,
                width: desiredWidth,
                pointerEvents: "none",
              }}
            />
            </div>
          ))}
          </div>
        </div>
        {/* <div className={styles.pdfNavigatorFooter}>
        <button 
        className={styles.uploadButton}
        onClick={()=>{setDoNext(true);}}
        >
          NEXT
        </button>
      </div> */}
      </div>
    </div>

  );
};

export default PdfNavigator;
