import clsx from 'clsx';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import BomTile from '../../bomTile/BomTile';
import { useRightWindowStore } from '../../../RightWindow/RightWindowStore';
import { BNPLSTATUS, creditLimitStatus, localStorageKeys, routes } from 'src/renderer2/common';
import { useBomReviewStore } from '../../BomReview/BomReviewStore';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer2/models/bomReview.model';
import { useFieldArray } from 'react-hook-form';
import { commomKeys, dateTimeFormat, getFloatRemainder, getValUsingUnitKey, newPricingPrefix, orderIncrementPrefix, priceUnits, reactQueryKeys, uploadBomConst, useBuyerCheckOutNode, useBuyerSettingStore, useCreatePoStore, useGlobalStore, useSaveUserActivity } from '@bryzos/giss-ui-library';
import { useLocation, useNavigate } from 'react-router-dom';
import { calculateBuyerTotalOrderWeightForGear } from 'src/renderer2/utility/pdfUtils';
import useGetAvailableCreditLimit from 'src/renderer2/hooks/useGetAvailableCreditLimit';
import { useBomPdfExtractorStore } from '../../BomPdfExtractor/BomPdfExtractorStore';
import CreatePoTile from './CreatePoTile';
import useCreatePoPriceCalculation from 'src/renderer2/hooks/useCreatePoPriceCalculation';
import dayjs from 'dayjs';
import usePostDraftPo from 'src/renderer2/hooks/usePostDraftPo';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import { useQueryClient } from '@tanstack/react-query';
import { useDebouncedValue } from '@mantine/hooks';
interface FormErrorData {
    qty?: boolean;
    product?: boolean;
    qtyEmpty?: boolean;
}

interface FormErrors {
    [key: number]: FormErrorData;
}

let emptyCartItem = {
    descriptionObj: '',
    description: '',
    qty: '',
    qty_unit: '',
    price_unit: '',
    product_tag: '',
    domestic_material_only: false,
    qtyUnitM: [],
    priceUnitM: [],
    line_session_id: '',
}

const CreatePoTable = forwardRef<any, any>((
    {
        styles,
        createPoContainerRef,
        formInputGroupRef,
        hidePoLineScroll,
        setHidePoLineScroll,
        addPoLineTableRef,
        bomUploadResult,
        products,
        userPartData,
        sessionId,
        searchStringData,
        setSearchString,
        setDisableBidBuyNow,
        setOpenDeliveryToDialog,
        scrollToTop,
        currentBomData,
        scrollPoHeaderToBottom,
        setFocusJobPoInput,
        scrollerRef,
        setMaxScrollHeight,
        setCurrentBomData,
        isHeaderDetailsConfirmed,
        initializePoHeaderForm,
        isSavedBom,
        setOpenErrorDialog,
        setErrorMessage,
        setCameFromSavedBom,
        maxScrollHeight,
        isProgrammaticScroll,
        setIsProgrammaticScroll,
        cameFromSavedBom,
        pricingBrackets,
        isCreatePOModule,
        poHeaderFormWatch,
        setBomUploadResult,
        navigateWithConfirmation,
        saveUserActivity,
        getDeliveryDateData,
        setCreatePoSessionId
    }, ref
) => {
    const navigate = useNavigate();
    const location = useLocation();
    const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
    const productMapping = useGlobalStore((state: any) => state.productMapping);
    const bomSummaryViewFilter = useCreatePoStore((state: any) => state.bomSummaryViewFilter);
    const setBomSummaryViewFilter = useCreatePoStore((state: any) => state.setBomSummaryViewFilter);
    const bomProductMappingDataFromSavedBom = useCreatePoStore((state: any) => state.bomProductMappingDataFromSavedBom);
    const createPoDataFromSavedBom = useCreatePoStore((state: any) => state.createPoDataFromSavedBom);
    const createPoData = useCreatePoStore((state: any) => state.createPoData);
    const uploadBomInitialData = useCreatePoStore((state: any) => state.uploadBomInitialData);
    const setBomProductMappingSocketData = useCreatePoStore((state: any) => state.setBomProductMappingSocketData);
    const buyerSetting = useBuyerSettingStore((state: any) => state.buyerSetting);
    const setCreatePoData = useCreatePoStore((state: any) => state.setCreatePoData);
    const setUploadBomInitialData = useCreatePoStore((state: any) => state.setUploadBomInitialData);
    const setIsCreatePoDirty = useCreatePoStore((state: any) => state.setIsCreatePoDirty);
    const setProps = useRightWindowStore((state: any) => state.setProps);
    const props = useRightWindowStore((state: any) => state.props);
    const showCommonDialog = useDialogStore((state: any) => state.showCommonDialog);
    const resetDialogStore = useDialogStore((state: any) => state.resetDialogStore);
    const setOpenLeftPanel = useLeftPanelStore((state: any) => state.setOpenLeftPanel);
    const setDisplayLeftPanel = useLeftPanelStore((state: any) => state.setDisplayLeftPanel);
    const leftPanelData = useLeftPanelStore((state: any) => state.leftPanelData);
    const setLeftPanelData = useLeftPanelStore((state: any) => state.setLeftPanelData);
    const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);
    const resetHeaderConfig = useGlobalStore((state: any) => state.resetHeaderConfig);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();
    const logUserActivity = useSaveUserActivity();

    const { setScrollToBomLine, scrollToBomLine } = useRightWindowStore();

    const lastModifiedBomRef = useRef(null);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const analyticRef = useRef();
    
    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    // const [viewIndex, setViewIndex] = useState(0);
    const viewIndex = useBomReviewStore((state) => state.viewIndex);
    const achCredit = {
        title: 'Cash In Advance',
        value: 'ach_credit',
        available: true,
        changeTitle: <span className={'w100'}>Cash In Advance</span>
    };
    const bnplCredit = {
        title: 'Net 30 Terms',
        value: 'bryzos_pay',
        available: true,
        changeTitle: <span className={'w100'} onClick={() => { setValue('payment_method', 'bryzos_pay'); setCreatePoData(getValues()); }} >Net 30 Terms (Setup)</span>
    }
    const [paymentMethods, setPaymentMethods] = useState([{ ...achCredit }, { ...bnplCredit }]);
    const [pageIndex, setPageIndex] = useState(0);
    const [filteredFields, setFilteredFields] = useState<any>([]);
    const [initialData, setInitialData] = useState(location.pathname !== routes.savedBom ? createPoData : null);
    // Flag to prevent infinite loops when programmatically setting scrollTop
    const startIndex = pageIndex * 20;
    const endIndex = (pageIndex + 1) * 20;
    let viewLineStatusIndex = 0;
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const lineItemsToLoad = 20;
    const itemsToAddOnScroll = lineItemsToLoad/4;
    const [createPoResultCopy, setCreatePoResultCopy] = useState<any>({});
    const [pageCount, setPageCount] = useState(0);
    const [disableReviewCompleteButton, setDisableReviewCompleteButton] = useState(true);
    const [didScrollToPreviousSavedBomLine, setDidScrollToPreviousSavedBomLine] = useState(false);
    const [isCartValid, setIsCartValid] = useState(true);
    const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    const [sendInvoicesToEmailData, setSendInvoicesToEmailData] = useState('');
    const [todayDate, setTodayDate] = useState('');
    const { setPdfUrl, setBomUploadID, setPdfFileName } = useBomPdfExtractorStore();
    const saveBuyerCheckout = useBuyerCheckOutNode();
    const postDraftPo = usePostDraftPo();
    const queryClient = useQueryClient();
    const [disableCloseAnalytic, setDisableCloseAnalytic] = useState(true);
    const [lineSessionId, setLineSessionId] = useState<any>([]);
    const [debouncedSearchString, cancelDebouncedSearchString] = useDebouncedValue(searchStringData, 400);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [lineSessionIdChanges, setLineSessionIdChanges] = useState<string | null>(null);
    analyticRef.current = disableCloseAnalytic && !resetHeaderConfig;
    const createPoResultCopyLength = Object.keys(createPoResultCopy || {})?.length || 0;
    const [rowHeight, setRowHeight] = useState(182);

    const {
        control,
        register,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        handleSubmit,
        errors
    } = useGenericForm(bomReviewSchema, {
        defaultValues: {
            'cart_items': [],
            'freight_term': "Delivered"
        }
    });


    const { fields, append, remove } = useFieldArray({
        control,
        name: "cart_items"
    });

    useEffect(()=>{
        setTimeout(()=>{
            const element = document.getElementById('createPoTile-0');
            if(element){
                const styles = getComputedStyle(element);
                const marginTop = parseFloat(styles.marginTop) || 0;
                const marginBottom = parseFloat(styles.marginBottom) || 0;
                
                const totalHeight = element.offsetHeight + marginTop + marginBottom;
                setRowHeight(totalHeight);
            }
        }, 200)
    },[])
    useEffect(() => {
        if (createPoContainerRef.current) {
            if (pageIndex > 0) {
                createPoContainerRef.current.style.overflowY = 'hidden';
            } else {
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    }, [pageIndex])

    useEffect(() => {
        if (createPoContainerRef.current) {
            if (!hidePoLineScroll) {
                if (addPoLineTableRef.current?.scrollTop > 100) {
                    createPoContainerRef.current.style.overflowY = 'hidden';
                } else if (pageIndex === 0) {
                    createPoContainerRef.current.style.overflowY = 'auto';
                }
                scrollerRef.current?.updateScrollPosition(createPoContainerRef.current?.scrollTop + rowHeight * 5 * pageIndex + addPoLineTableRef.current?.scrollTop);
            } else {
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    }, [hidePoLineScroll])

    useEffect(() => {
        if (sessionId) {
            const payload = {
                "data": {
                    "session_id": sessionId
                }
            }
            logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload }).catch(err => console.error(err));
        }
        return () => {
            cancelDebouncedSearchString()
            if (analyticRef.current && sessionId) {
                const payload = {
                    "data": {
                        "session_id": sessionId,
                        "close_status": "CANCEL"
                    }
                }
                logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload })
                    .then(() => setCreatePoSessionId(null))
                    .catch(err => console.error(err))
            }
        }
    }, [sessionId])


    // useEffect(() => {
    //     if (currentBomData?.id && createPoResultCopyLength > 0 && !didScrollToPreviousSavedBomLine) {
    //         const _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
    //         setDidScrollToPreviousSavedBomLine(true);
    //         setTimeout(() => {
    //             handleScrollToBomLine(_lastModifiedBom?.lineId, _lastModifiedBom?.input);
    //         }, 500);
    //     }
    // }, [currentBomData, createPoResultCopyLength])

    const checkAtLeastOneApproved = Object.values(createPoResultCopy).some((item: any) => item.lineStatus === 'APPROVED')
    const isInvalidBomForm = Object.values(createPoResultCopy).some((item: any, index: number) => {
        if (item.lineStatus === 'SKIPPED' || item.lineStatus === 'DELETED') {
            return false;
        } else if (item.lineStatus === 'APPROVED') {
            return formErrors[index]?.qty || formErrors[index]?.product || formErrors[index]?.qtyEmpty
        }
        return (
            item.lineStatus === 'PENDING'
        );
    })
    useEffect(() => {
        setDisableReviewCompleteButton(!orderInfoIsFilled || !checkAtLeastOneApproved || isInvalidBomForm);
    }, [orderInfoIsFilled, checkAtLeastOneApproved, isInvalidBomForm]);

    // useEffect(() => {
    //     setProps({ ...props, disableReviewCompleteButton, createPoResultCopy });
    // }, [disableReviewCompleteButton, createPoResultCopy])

    const getInitialData = () => {
        if (location.pathname === routes.bomUploadReview && location.state?.from === 'bomPdfExtractor') {
            return uploadBomInitialData;
        }
        return initialData;
    }

    useEffect(() => {
        setIsAllCartDataLoaded(false);
        // setValue('cart_items', []);
        if(isCreatePOModule){
            setTimeout(()=>{
                const cart_items = Array(4).fill({...emptyCartItem});
                setValue('cart_items', cart_items);
                initializeCreatePOData();
            }, 0)
        }
        reset();
    }, [initializePoHeaderForm]);

    // Use the tracked length in useEffect
    useEffect(() => {
        const formInputGroup = formInputGroupRef.current;
        // const addLineContainer = addPoLineTableRef.current;
        const tableHeight = rowHeight * ((pageIndex*5)+20);
        const minTableHeight = bomUploadResult.length === 0 ? 0 : rowHeight * bomUploadResult.length; 
        if (!!formInputGroup) {
            // setTimeout(() => {
                setMaxScrollHeight((formInputGroup?.scrollHeight + Math.max(tableHeight,minTableHeight)));
            // }, 200)
            setTimeout(()=>{
                if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + addPoLineTableRef.current.scrollTop);
            }, 200)
        }
    }, [pageIndex, bomUploadResult]);  // Now depends on the tracked length

    useEffect(() => {
        const initializeData = async () => {
            if (initialData && location.pathname === routes.savedBom) {
                await initializeCreatePOData();
            }
        }
        initializeData();
    }, [initialData, location.pathname])

    useEffect(() => {
        const initializeData = async () => {
            if (isHeaderDetailsConfirmed) {
                await initializeCreatePOData();
            }
        };

        initializeData();
    }, [isHeaderDetailsConfirmed])


    useEffect(() => {
        if (debouncedSearchString && lineSessionIdChanges) {
            handleCreatePOSearch(searchStringData, null, lineSessionIdChanges)
        }
    }, [debouncedSearchString])



    const initializeCreatePOData = async () => {
        try {
            setIsAllCartDataLoaded(true);
            const _intialData = getInitialData();
            let buyingPreference = {...buyerSetting};
            initializeBnplSettings()

            if (_intialData) {
                setValue('id', _intialData?.id ?? '');
                if (_intialData?.cameFromSavedBom && !isHeaderDetailsConfirmed && initializePoHeaderForm) {
                    handleHeaderDetailsFill()
                } else {
                    if (!isHeaderDetailsConfirmed && initializePoHeaderForm) {
                        initializePoHeaderForm(_intialData);
                    }

                    if (_intialData?.cart_items?.length > 0 && isHeaderDetailsConfirmed) {
                        setValue('seller_price', _intialData?.seller_price ?? '0');
                        setValue('price', _intialData?.price ?? '0');
                        await handlePriceIntegration(undefined, _intialData.cart_items)
                        setBomUploadResult(_intialData.cart_items);
                        
                        // setValue('cart_items', _intialData.cart_items);
                        // if (_intialData?.cart_items?.length > 0 && !(isSavedBom && createPoDataFromSavedBom)) {
                        //     _intialData?.cart_items?.forEach((product: any, index: number) => {
                        //         if (product?.descriptionObj && Object.keys(product.descriptionObj).length > 0) {
                        //             resetQtyAndPricePerUnitFields(index, product.descriptionObj)
                        //             setDisableBidBuyNow(true)
                        //         };
                        //         // updateValue(index)
                        //     });
                        //     if (location.pathname !== routes.savedBom) await handlePriceIntegration();
                        // }
                    }
                    setValue('payment_method', _intialData?.payment_method ?? '');
                    setValue('sales_tax', _intialData?.sales_tax ?? 0);
                    if (_intialData?.bom_id) {
                        setValue('bom_id', _intialData?.bom_id ?? '');
                    }
                    setValue('is_draft_po', _intialData?.is_draft_po ?? false);
                    if (isSavedBom && createPoDataFromSavedBom) {
                        setValue('price', _intialData?.material_total)
                        setValue('totalPurchase', _intialData?.total_purchase)
                        setValue('depositAmount', _intialData?.deposit)
                        setValue('subscriptionAmount', _intialData?.subscription)
                        setValue('totalWeight', parseInt(_intialData?.total_weight) ?? 0);
                    } else {
                        console.log("calculateBuyerTotalOrderWeightForGear ####  ##################  ", calculateBuyerTotalOrderWeightForGear(_intialData.cart_items), _intialData.cart_items)
                        // setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(_intialData.cart_items));
                        // calculateMaterialTotalPrice();
                    }
                }
            } else {
                setValue('sales_tax', 0);
                setSendInvoicesToEmailData(buyingPreference.send_invoices_to);
            }
            setDefaultPaymentOption(buyingPreference, paymentMethods, false);
            setIsAllCartDataLoaded(true);
        } catch (err) {
            setOpenErrorDialog(true);
            setErrorMessage("Something went wrong. Please try again in sometime");
            setShowLoader(false);
            console.error(err)

        } finally {
            setCreatePoData(null);
        }
    }
    useEffect(()=>{
        if(buyerSetting){
            initializeBnplSettings()
        }
    },[buyerSetting])

    const initializeBnplSettings = ()=>{
        const {buyerSetting} = useBuyerSettingStore.getState();
        if(buyerSetting?.bnpl_settings){ 
            setValue('availableCreditLimit', buyerSetting?.bnpl_settings?.bryzos_available_credit_limit)
            setValue('bnplStatus', buyerSetting?.bnpl_settings?.bnpl_status);
            if(buyerSetting?.bnpl_settings?.requested_credit_limit){
                setValue('requestedCreditLimit', buyerSetting?.bnpl_settings?.requested_credit_limit)
            }else{
                setValue('requestedCreditLimit', 0)
            }
            setValue('max_restricted_amount' , buyerSetting?.bnpl_settings?.max_restricted_amount ??"0")
        }else{
            setValue('availableCreditLimit', 0)
            setValue('bnplStatus', BNPLSTATUS.REJECTED);
            setValue('requestedCreditLimit', 0)
            setValue('max_restricted_amount' , "0")
        }
    }

    const handleHeaderDetailsFill = () => {
        setCameFromSavedBom(true);
        initializePoHeaderForm(initialData);
    }
    const initializeBomTileData = (index: number) => {
        const selectedProduct = watch(`cart_items.${index}.selected_products`);
        if (selectedProduct?.length > 0) {
            const product = productMapping[selectedProduct[0]];
            if (product) {
                setValue(`cart_items.${index}.descriptionObj`, product);
                setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options?.split(","));

                // Only set qty_unit if 'Ea' is not in QUM options
                const qumOptions = product.QUM_Dropdown_Options?.split(",");
                if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
                    setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
                }
            }
        }
    }

    useEffect(() => {
        if (bomUploadResult?.length > 0) {
            let createPoResultCopyObj: any = {};
            for (let i = 0; i < bomUploadResult.length; i++) {
                setDisableBidBuyNow(true);
                if (i < lineItemsToLoad) {
                    setValue(`cart_items.${i}`, { ...bomUploadResult[i] });
                    // append({ ...bomUploadResult[i] })
                }
                initializeBomTileData(i)
                validateSavedBomCreatePo(bomUploadResult[i])
                createPoResultCopyObj[i] = bomUploadResult[i];
            }
            if (bomUploadResult.length > lineItemsToLoad) {
                setPageCount(Math.ceil((bomUploadResult.length - lineItemsToLoad) / (lineItemsToLoad / 4)));
            } else {
                setPageCount(0);
            }
            setCreatePoResultCopy(createPoResultCopyObj);

        }
    }, [bomUploadResult])
// console.log("bomUploadResult", bomUploadResult, createPoResultCopy, watch('cart_items'), fields);
    useEffect(() => {
        if(isAllCartDataLoaded){
            // setValue('cart_items', []);
            setTodayDate(new Date());
            setTimeout(()=>{
                for (let i = 0; i < (lineItemsToLoad); i++) {
                    // append({ ...emptyCartItem })
                    setValue(`cart_items.${i}`, { ...emptyCartItem })
                }
            }, 10)
        }
        // const cart_items = Array(lineItemsToLoad).fill({ ...emptyCartItem });
        // setCreatePoResultCopy(cart_items);
    }, [isAllCartDataLoaded])

    useEffect(() => {
        if (createPoResultCopyLength > 0) {
            setShowLoader(false);
        }
    }, [createPoResultCopyLength])

    // useEffect(() => {
    //     if (createPoResultCopyLength > 0) {
    //         handleFilterFieldsData(0, true);
    //         setPageIndex(0);
    //         setIsProgrammaticScroll(true);
    //         setTimeout(() => {
    //             if (createPoContainerRef.current?.scrollTop < 230 && addPoLineTableRef.current) {
    //                 addPoLineTableRef.current.style.overflowY = 'hidden';
    //             }
    //             addPoLineTableRef.current?.scrollTo({ top: 0 });
    //             setIsProgrammaticScroll(false);
    //         }, 200)

    //     }
    // }, [bomSummaryViewFilter])

    const getFilteredFieldsData = (checkAtLeastOneValid: boolean = true) => {
        // const filteredFieldsData = Object.values(createPoResultCopy).filter((item: any, index: number) => {
        //     const lineStatus = item?.lineStatus
        //     const checkValidation = ((bomSummaryViewFilter === 'all') || (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) || (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending))
        //     return checkValidation;
        // })
        // setFilteredFields(filteredFieldsData);
        const formInputGroup = formInputGroupRef.current;
        const tableHeight = rowHeight * createPoResultCopyLength;
        // setMaxScrollHeight((formInputGroup?.scrollHeight + tableHeight));
        return createPoResultCopy;
    }

    const handleFilterFieldsData = (nextIndex: number=pageIndex, filterList: any = createPoResultCopy) => {
        // const filteredFieldsData = getFilteredFieldsData();
        // const pageCount = filteredFieldsData.length <= lineItemsToLoad ? 0 : Math.ceil((filteredFieldsData.length - lineItemsToLoad) / (lineItemsToLoad / 4));
        // setPageCount(pageCount);
        let list;
        const nextList = [];
        for(let i = 0; i < lineItemsToLoad; i++){
            const index = nextIndex * itemsToAddOnScroll + i;
            if(filterList[index]) console.log("filterList[index] >>>>>>.>> ", filterList[index], index)
            nextList.push(filterList[index] ? {...filterList[index]} : {...emptyCartItem});
        }
        // const multiplier = isAtBottom ? 1 : -1;
        // for(let i = 0; i < itemsToAddOnScroll; i++){
        //     let index;
        //     if(!isAtBottom) index = ((pageIndex * itemsToAddOnScroll) - itemsToAddOnScroll + i);
        //     else index = (pageIndex * itemsToAddOnScroll) + lineItemsToLoad + i;
        //     nextList[i] = createPoResultCopy[index] ? {a:index,...createPoResultCopy[index]} : {a:index,...emptyCartItem}
        // }

        // if(isAtBottom){
        //     list = watch('cart_items').slice(5,20).concat(nextList);
        // }else{
        //     list = nextList.concat(watch('cart_items').slice(0, 15));
        // }
        // console.log("list >>>>>>.>> ", list)
        setValue('cart_items', nextList);
        // if (adjustScroll) {
        //     if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current?.scrollTop);
        // }
    }

    // console.log("watch('cart_items') >>>>>>.>> ", pageIndex, watch('cart_items'))


    useEffect(() => {
        if (location.pathname === routes.savedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)) {
            reset();
            setViewIndex(0);
            setBomSummaryViewFilter('all')
            setInitialData(null);
            setBomProductMappingSocketData(null);
            setScrollToBomLine(null)
            if (bomProductMappingDataFromSavedBom) {
                setInitialData({ ...bomProductMappingDataFromSavedBom });
                setCurrentBomData({ ...bomProductMappingDataFromSavedBom });
                setPdfFileName(bomProductMappingDataFromSavedBom?.actual_file_name);
                setPdfUrl(bomProductMappingDataFromSavedBom?.s3_url);
                setBomUploadID(bomProductMappingDataFromSavedBom?.id);
                let formattedUploadBomHeaderData = {
                    delivery_date: bomProductMappingDataFromSavedBom?.delivery_date,
                    shipping_details: bomProductMappingDataFromSavedBom?.shipping_details,
                    order_type: bomProductMappingDataFromSavedBom?.type,
                    internal_po_number: bomProductMappingDataFromSavedBom?.title,
                    bom_id: bomProductMappingDataFromSavedBom?.id,
                }
                setUploadBomInitialData({ ...formattedUploadBomHeaderData })
            } else if (createPoDataFromSavedBom) {
                handleCreatePoDataFromSavedBom();
            }
        }
    }, [location.pathname, createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])

    const handleCreatePoDataFromSavedBom = () => {
        const _createPoDataFromSavedBom = { ...createPoDataFromSavedBom };
        const cartItem = _createPoDataFromSavedBom?.result?.map((item, i) => ({
            bom_line_id: item.id || i,
            lineStatus: item.status,
            originalStatus: item.original_line_status || item.status,
            confidence: item.confidence,
            product_tag: item.product_tag,
            description: item.description,
            specification: item.specification,
            search_string: item.search_string,
            matched_products: item.matched_products,
            selected_products: item.selected_products,
            current_page: item.current_page,
            total_pages: item.total_pages,
            product_index: item.product_index,
            grade: item.grade,
            qty: item.qty,
            qty_unit: item.qty_unit,
            length: item.length,
            weight_per_quantity: item.weight_per_quantity,
            matched_product_count: item.matched_product_count,
            price_unit: item.price_unit,
            price: item.price_per_unit,
            line_weight: item.line_weight ?? '0.00',
            extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
            domesticMaterialOnly: item?.domestic_material_only || false,
            product_id: item.product_id,
            draft_line_id: item.id
        }));

        if (cartItem) {
            cartItem.forEach((item: any, index: number) => {
                if (item?.product_id) {
                    const product = productMapping[item.product_id];
                    if (product) {
                        item.descriptionObj = product;
                    }
                } else if (item?.selected_products?.length) {
                    const selectedProducts = item.selected_products;
                    const hasSelectedProducts = selectedProducts.length > 0;
                    if (hasSelectedProducts) {
                        const product = productMapping[selectedProducts[0]];
                        // Directly set the values on the cartItem object
                        item.descriptionObj = product;
                    }
                }
            });
        }
        setValue('cart_items', cartItem);
        setInitialData({ ...createPoDataFromSavedBom, cart_items: cartItem });
    }

    const getLastModifiedBom = (key: string): string | null => {
        try {
            const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if (lastModifiedBom) {
                const lastModifiedBomData = JSON.parse(lastModifiedBom);
                if (key in lastModifiedBomData) {
                    return lastModifiedBomData[key];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (e) {
            console.warn('Error checking key in localStorage', e);
            return null;
        }
    };

    const validateSavedBomCreatePo = useCallback((item: any) => {
        // const item = watch(`cart_items.${index}`);
        let descriptionObj = item?.descriptionObj;
        if (item?.selected_products?.length && !descriptionObj) {
            const selectedProducts = item.selected_products;
            const hasSelectedProducts = selectedProducts.length > 0;
            if (hasSelectedProducts) {
                const product = productMapping[selectedProducts[0]];
                // Directly set the values on the cartItem object
                descriptionObj = product;
            }
        }
        if (descriptionObj && Object.keys(descriptionObj).length > 0) {
            const _selected = descriptionObj;

            if (_selected) {
                const qtyVal = +item.qty || 0;
                const qtyUnit = item.qty_unit;
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options?.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal && orderIncrement) {
                    // console.log("qtyVal", qtyVal, orderIncrement);
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        setFormErrors(prevErrors => {
                            const newErrors = { ...prevErrors };
                            delete newErrors[item.lineStatusIndex];
                            return newErrors;
                        });

                        // clearErrors(`cart_items.${index}.qty`);
                        // trigger(`cart_items.${index}.qty`);
                        return true;
                    } else {
                        if (_selected && (item.lineStatus === uploadBomConst.lineItemStatus.approved || item.lineStatus == uploadBomConst.lineItemStatus.pending)) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [item.lineStatusIndex]: {
                                    qty: true,
                                    product: false,
                                    qtyEmpty: false
                                }
                            }));
                        }
                        // if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` }, { shouldFocus: false })

                        // setValue(`cart_items.${index}.extended`, 0);
                        // setValue(`cart_items.${index}.seller_extended`, 0);
                    }
                }
                else {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [item.lineStatusIndex]: {
                            qty: false,
                            product: false,
                            qtyEmpty: true
                        }
                    }));
                }
            }
        } else {
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [item.lineStatusIndex]: {
                    qty: false,
                    product: true,
                    qtyEmpty: false
                }
            }));
        }
    }, [watch, getValues, productMapping, setValue]);

    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus?: string | null) => {
        if (!elementToFocus) return;
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="qty-input-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else {
            const childElement = parentElement.querySelector('[id^="combo-box-demo"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const handleLineStatusChange = () => {
        if (bomSummaryViewFilter !== 'all') {
            const filteredFieldsData = getFilteredFieldsData(false);
            const list = filteredFieldsData.slice(pageIndex * 5, pageIndex * 5 + 20)
            if (list.length === 0 && pageIndex === 0) {
                setBomSummaryViewFilter('all');
            } else {
                setValue(`cart_items`, filteredFieldsData.slice(pageIndex * 5, pageIndex * 5 + 20));
            }
        }
    }

    const scrollBomLineToSpecificIndex = (bomLineId: string, elementToFocus?: string | null) => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            setIsProgrammaticScroll(true);
            table.scrollTo({ top: elementOffsetTop - 50 });
            setTimeout(() => {
                setIsProgrammaticScroll(false);
            }, 200)
        };
        const table = addPoLineTableRef.current;
        const element = document.getElementById(bomLineId);

        if (table && element) {
            scrollToElement(table, element);
            // focusChildElement(element, elementToFocus);
        }
        setScrollToBomLine(null)
    }

    // useEffect(() => {
    //     if (scrollToBomLine) {
    //         handleScrollToBomLine(scrollToBomLine, "description")
    //     }
    // }, [scrollToBomLine])
    // console.log("filteredFields", filteredFields);

    const handleScrollToBomLine = (index: number, elementToFocus?: string | null, isDragging: boolean = false) => {
        const bomList = Object.values(createPoResultCopy);
        // const index = i;
        if (index !== -1) {
            const _pageIndex = (index + 1) - 15 <= 0 ? 0 : Math.ceil(((index + 1) - 15) / 5) + 1;
            setPageIndex(_pageIndex);
            const nextList = [];
            for(let i = 0; i < 20; i++){
                const lineItemIndex = _pageIndex * 5 + i;
                nextList[i] = createPoResultCopy[lineItemIndex] ? {...createPoResultCopy[lineItemIndex]} : {...emptyCartItem}
            }
            setValue('cart_items', nextList);
            setIsProgrammaticScroll(true);
            if (!isDragging) scrollPoHeaderToBottom()
            setTimeout(() => {
                if (!isDragging && createPoContainerRef.current) {
                    const element = document.getElementById(`createPoTile-${index}`);
                    const position = createPoContainerRef.current.scrollTop + _pageIndex * rowHeight * 5 + element?.offsetTop - 200;
                    if (scrollerRef.current) scrollerRef.current.updateScrollPosition(position);
                }
                scrollBomLineToSpecificIndex(`createPoTile-${index}`, elementToFocus);
                setTimeout(() => {
                    setIsProgrammaticScroll(false);
                }, 200)
            }, 500)
        }
    }

    const handleLoadNextItems = (index: number, isAtBottom: boolean, updateScroll: boolean = true) => {
        const { scrollTop } = addPoLineTableRef.current;
        handleFilterFieldsData(index);
        setPageIndex(index);
        setIsProgrammaticScroll(true);
        setTimeout(() => {
            if (addPoLineTableRef.current) addPoLineTableRef.current.scrollTop = isAtBottom ? scrollTop - rowHeight * 5 : scrollTop + rowHeight * 5;
            // handleViewIndexChange();
        }, 100)
        if (scrollerRef.current && updateScroll) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + scrollTop);
        // Reset flag after a short delay
        setTimeout(() => {
            setIsProgrammaticScroll(false);
        }, 200);
    }

    const handleLineItemScroll = () => {
        // Skip if the scroll is programmatic
        if (!addPoLineTableRef.current || isProgrammaticScroll) return;
        const { scrollTop, scrollHeight, clientHeight } = addPoLineTableRef.current;
        const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5; // 5px threshold
        // Unfocus any focused elements when scrolling
        const focusedElement = document.activeElement;
        if (focusedElement instanceof HTMLElement) {
            // Check if element is out of view
            const rect = focusedElement.getBoundingClientRect();
            const isOutOfView = (
                rect.bottom < 0 || 
                rect.top > window.innerHeight ||
                rect.right < 0 || 
                rect.left > window.innerWidth
            );
            
            if (isOutOfView) {
                focusedElement.blur();
            }
        }
        if (isAtBottom) {
            handleLoadNextItems(pageIndex + 1, true, false)
            return;
        }
        else if (pageIndex > 0 && scrollTop < rowHeight) {
            handleLoadNextItems(pageIndex - 1, false, false)
            return;
        } 
        if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + scrollTop);
        // else {
        //     handleViewIndexChange();
        // }    
    }

    // const handleViewIndexChange = () => {
    //     const container = addPoLineTableRef.current;
    //     if (container) {
    //         const firstRow = container.querySelector('tbody tr:first-child');
    //         if (firstRow) {
    //             const { scrollTop } = container;
    //             const rect = firstRow.getBoundingClientRect();

    //             // Get computed styles to access margins
    //             const computedStyle = window.getComputedStyle(firstRow);

    //             // Calculate total height including margins
    //             const marginTop = parseInt(computedStyle.marginTop, 10);
    //             const marginBottom = parseInt(computedStyle.marginBottom, 10);

    //             // Total height = height + margin-top + margin-bottom
    //             const firstRowHeight = rect.height + marginTop + marginBottom;
    //             const index = Math.floor(scrollTop / firstRowHeight);

    //             // // Clear existing timeout
    //             // if (debounceTimeoutRef.current) {
    //             //     clearTimeout(debounceTimeoutRef.current);
    //             // }

    //             // // Set new timeout to debounce the setViewIndex call
    //             // debounceTimeoutRef.current = setTimeout(() => {
    //             // }, 400);
    //             setViewIndex(pageIndex * 5 + index);


    //         } else {
    //             console.warn('First row not found');
    //         }
    //     }
    // };

    const openAddLineTab = () => {
        if (!addPoLineTableRef.current || !hidePoLineScroll) return;
        const container = addPoLineTableRef.current;
        if (container.scrollTop > 0 && orderInfoIsFilled && createPoContainerRef.current) {
            createPoContainerRef.current.scrollTo({
                top: createPoContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            container.style.overflowY = 'auto';
            const formInputHeight = formInputGroupRef.current?.clientHeight;
            setTimeout(() => {
                if (scrollerRef.current) scrollerRef.current.updateScrollPosition(pageIndex * rowHeight * 5 + container.scrollTop + formInputHeight);
            }, 400)
        }
    }

    const saveModifiedBom = (index: number, input: any) => {
        try {
            if (currentBomData?.id && watch(`cart_items.${index}`)?.bom_line_id) {
                const data = {
                    [currentBomData?.id]: { lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex }
                }
                const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
                if (_lastModifiedBom) {
                    const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                    _lastModifiedBomData[currentBomData?.id] = { lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex };
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
                } else {
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
                }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    const handleScrollerDrag = (newScrollPosition: number) => {
        setIsProgrammaticScroll(true);
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }
        const indexBasedOnScrollPosition = Math.ceil((newScrollPosition - createPoContainerRef.current.scrollTop)/rowHeight);
        console.log("indexBasedOnScrollPosition >>>>>>.>> ", newScrollPosition, createPoContainerRef.current.scrollTop, indexBasedOnScrollPosition)
        if (newScrollPosition >= 0 && newScrollPosition < 204) {
            // debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                handleScrollToBomLine(0, null, true);
                setIsProgrammaticScroll(false);
            // }, 200)
        }
        else if (scrollerRef.current?.isAtBottom(100)) {
            // createPoContainerRef.current.scrollTop = newScrollPosition;
            // Set new timeout to debounce the setViewIndex call
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                handleScrollToBomLine(indexBasedOnScrollPosition, null, true);
            }, 200);
        }
        else if (newScrollPosition >= 204) {

            // Set new timeout to debounce the setViewIndex call
            // console.log("newScrollPosition", newScrollPosition, maxScrollHeight);
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                // const index = Math.floor((newScrollPosition - 162) / rowHeight);
                // const bomLineId = createPoResultCopy[index]?.bom_line_id;
                handleScrollToBomLine(indexBasedOnScrollPosition, null, true);
            }, 200);
        }

        // if(newScrollPosition >= 0 && newScrollPosition < 204){
        //     headerScroll = newScrollPosition;
        //     tableScroll = 0;
        //     if(pageIndex !== 0){
        //         setPageIndex(0);

        //     }
        // }else {
        //     headerScroll = 204;
        //     tableScroll = newScrollPosition - 204;
        // }
        // if(createPoContainerRef.current)
        //     createPoContainerRef.current.scrollTop = headerScroll;

        // // Clear existing timeout

        // if(addPoLineTableRef.current)
        //     addPoLineTableRef.current.scrollTop = tableScroll;

    }
    
    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = getValues('cart_items');
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.lineStatus !== "SKIPPED" || item?.lineStatus === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 && 
                   Boolean(item.qty_unit) &&
                   Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    useEffect(() => {
        const subscription = watch((_, { name }) => {
            if (name?.startsWith('cart_items')) {
                setIsCartValid(validateCart());
            }
        });
        return () => subscription.unsubscribe();
    }, [watch]);


    const navigateToSettings = () => {
        navigate(routes.buyerSettingPage, { state: { from: 'createPo' } })
    }

        
    const handleSavePo = () => {
        setShowLoader(true);
        const data = watch();
        const poHeaderFormData = poHeaderFormWatch();
        resetDialogStore();
        setIsCreatePoDirty(false);
        const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
        const shippingDetails = poHeaderFormData.shipping_details;
        shippingDetails.line2 = poHeaderFormData.shipping_details.line2?.trim() ? poHeaderFormData.shipping_details.line2 : null;
        const payload = {
            "draft_id": watch('is_draft_po') ? data.id : '',
            "internal_po_number": poHeaderFormData.internal_po_number,
            "shipping_details": poHeaderFormData.shipping_details,
            "delivery_date": dayjs(poHeaderFormData.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
            "payment_method": data.payment_method || undefined,
            "price": String(data.price),
            "sales_tax": data.sales_tax,
            "freight_term": "Delivered",
            "cart_items": formatCartItems(Object.values(createPoResultCopy), data.id),
            "checkout_local_timestamp": localDateTime,
            "order_type": poHeaderFormData?.order_type ?? '',
            "order_size": String(data?.totalWeight) ?? ''
        };
        postDraftPo.mutate(payload, {
            onSuccess: () => {
                setShowLoader(false);
                navigateWithConfirmation(false, routes.savedBom);
                setDisplayLeftPanel(false);
                setOpenLeftPanel(true);
            },
            onError: (error) => {
                showCommonDialog(null,error?.message || commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                setShowLoader(false);
            }
        });


    }

    const { updateLineProduct, calculateMaterialTotalPrice, pricePerUnitChangeHandler, removeLineItem, handleCreatePOSearch, saveUserLineActivity, handlePriceIntegration, updateLineItem, resetPricePerUnitFields, resetQtyAndPricePerUnitFields, willGearUpdate, handleSubmitValidation, formatCartItems, getCartItems, setDefaultPaymentOption }
        = useCreatePoPriceCalculation(setValue, clearErrors, userPartData, getValues, remove, sessionId, watch, trigger, setError, initialData, setFormErrors, formErrors, createPoResultCopy, pageIndex, itemsToAddOnScroll, handleFilterFieldsData, setCreatePoResultCopy, poHeaderFormWatch, bnplCredit, setSelectedProduct);


    const disableFormValidation = !orderInfoIsFilled ||
        Object.keys(errors).length > 0 ||
        !handleSubmitValidation ||
        watch('totalWeight') < pricingBrackets?.[0]?.min_weight ||
        !isCartValid ||
        !watch('totalPurchase');


    // Disable place order button if any validation fails
    const disablePlaceOrderButton = disableFormValidation ||
        poHeaderFormWatch('shipping_details.validating_state_id_zip') || (watch('payment_method') === 'bryzos_pay' && (watch('bnplStatus') === BNPLSTATUS.ON_HOLD || (watch('bnplStatus') === BNPLSTATUS.RESTRICTED && Number(watch(`totalPurchase`)) > Number(watch('max_restricted_amount'))))) ||
        !watch('payment_method') ||
        (getValues('payment_method') === 'bryzos_pay' && (
            buyerSetting?.bnpl_settings?.is_approved === null ||
            watch('totalPurchase') > (watch('availableCreditLimit') || 0)
        ));

        
    const getExportPoData = ()=>{
        const paymentMethod = watch('payment_method') || 'bryzos_pay';
        return { ...watch(), ...poHeaderFormWatch(), selectedOptionPayment: paymentMethod };
    }


    const onSubmit = (data) => {
        const date = new Date()
        if(dayjs(date).format('M/D/YYYY') === dayjs(todayDate).format('M/D/YYYY')){
            setShowLoader(true);
            const totalPurchaseValue = data.price;
            const poHeaderFormData = poHeaderFormWatch();
            const totalSellerPurchaseValue = parseFloat(data.seller_price).toFixed(2);
            const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
            poHeaderFormData.shipping_details.line2 = poHeaderFormData.shipping_details.line2?.trim() || null;
            const payload = {
                "data": {
                    "internal_po_number": poHeaderFormData.internal_po_number,
                    "shipping_details": poHeaderFormData.shipping_details,
                    "delivery_date": dayjs(poHeaderFormData.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                    "payment_method": data.payment_method,
                    "seller_price": totalSellerPurchaseValue, 
                    "price": String(totalPurchaseValue),
                    "sales_tax": data.sales_tax,
                    "freight_term": "Delivered",
                    "cart_items": formatCartItems(Object.values(createPoResultCopy)),
                    "checkout_local_timestamp": localDateTime,
                    "delivery_date_offset": poHeaderFormData.delivery_date_offset,
                    "order_type": poHeaderFormData?.order_type ?? '',
                    "order_size": String(data?.totalWeight) ?? ''
                }
            };

            if(watch('bom_id') && !watch('is_draft_po')){
                payload.data.bom_id = watch('bom_id')
            } else if (watch('is_draft_po') && watch('bom_id')){
                payload.data.draft_id = watch('bom_id')
            }
            setDisableCloseAnalytic(false)
            saveBuyerCheckout.mutateAsync(payload)
                .then(res => {
                    if (res.data.data.error_message) {
                        setOpenErrorDialog(true);
                        setErrorMessage(res.data.data.error_message);
                        setShowLoader(false);
                        saveUserActivity(null, res.data.data.error_message)
                        setDisableCloseAnalytic(true)
                    } else {
                        queryClient.invalidateQueries([reactQueryKeys.getUserPartData]);
                        if(watch('bom_id') && !watch('is_draft_po')){
                            setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        } else if (watch('is_draft_po') && watch('bom_id')){
                            setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        }
                        const payload = {
                            "data": {
                                "session_id": sessionId,
                                "close_status": "ACCEPT"
                            }
                        }
                        logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload})
                        .catch(err => console.error(err))
                        navigate(routes.orderConfirmationPage, { state: { poNumber: res.data.data, jobNumber: data.internal_po_number, sendInvoicesTo: sendInvoicesToEmailData, selectedOptionPayment: data.payment_method, } })
                        setCreatePoData(null);
                        saveUserActivity(res.data.data, null);
                    }
                })
                .catch(err => {
                    saveUserActivity(null, (err?.message ?? err));
                    setDisableCloseAnalytic(true)
                    setOpenErrorDialog(true);
                        setErrorMessage("Something went wrong. Please try again in sometime");
                        setShowLoader(false);
                    console.error(err)
                })
        }else{
            setOpenErrorDialog(true);
            setErrorMessage('Selected delivery date is incorrect, please select correct delivery date.');
            setValue('delivery_date', null)
            getDeliveryDateData();
            setTodayDate(new Date());
        }
    }

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        getInitialData,
        handleScrollerDrag,
        disableFormValidation,
        handleSavePo,
        getCartItems,
        getExportPoData,
        watch,
        // reset
    }), [getInitialData, handleScrollerDrag, disableFormValidation, handleSavePo, getCartItems, getExportPoData, watch]);

    useEffect(() => {
        const orderSummaryProps = {
            watch,
            paymentMethods,
            control,
            setValue,
            getValues,
            navigateToSettings,
            saveUserActivity,
            disablePlaceOrderButton,
            handleSubmit,
            onSubmit,
            disableReviewCompleteButton,
            createPoResultCopy,
            pricingBrackets,
            isCreatePOModule
        };
        setProps({ ...props, ...orderSummaryProps });

    }, [
        watch,
        watch('price'),
        watch('sales_tax'),
        watch('depositAmount'),
        watch('subscriptionAmount'),
        watch('totalPurchase'),
        watch('availableCreditLimit'),
        watch('payment_method'),
        watch('requestedCreditLimit'),
        watch('totalWeight'),
        disablePlaceOrderButton,
        disableReviewCompleteButton,
        currentBomData,
        createPoResultCopy,
        pricingBrackets,
        isCreatePOModule
    ]);

    return (
        <div style={{ overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden' }} className={clsx(styles.addPoLineTable)} onScroll={handleLineItemScroll} ref={addPoLineTableRef} onClick={() => { openAddLineTab() }}>
            <table >
                <thead>
                    <tr>
                        <th><span>LN</span></th>
                        <th><span>DESCRIPTION</span></th>
                        <th><span>QTY</span></th>
                        <th><span> $/UNIT</span></th>
                        <th colSpan={2}><span>EXT ($)</span></th>
                    </tr>
                </thead>
                <tbody>
                    {
                        watch('cart_items').map((item: any, _index: number) => {
                            const index = (pageIndex * itemsToAddOnScroll) + _index;
                            // if(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) console.log("watch(`cart_items.${index}.descriptionObj`) ####  ##################  ", index, pageIndex, itemsToAddOnScroll, _index);
                            viewLineStatusIndex++;


                            return (
                                <React.Fragment key={_index}>
                                    {
                                        <CreatePoTile
                                            index={_index}
                                            actualIndex={index}
                                            register={register}
                                            fields={fields}
                                            // updateValue={updateValue}
                                            updateLineProduct={updateLineProduct}
                                            products={products}
                                            setValue={setValue}
                                            watch={watch}
                                            errors={errors}
                                            control={control}
                                            getValues={getValues}
                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                            removeLineItem={removeLineItem}
                                            userPartData={userPartData}
                                            sessionId={sessionId}
                                            selectedProduct={selectedProduct}
                                            searchStringData={searchStringData}
                                            setSearchString={setSearchString}
                                            setLineSessionId={setLineSessionId}
                                            lineSessionId={lineSessionId}
                                            handleCreatePOSearch={handleCreatePOSearch}
                                            // apiCallInProgress={apiCallInProgress}
                                            saveUserLineActivity={saveUserLineActivity}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                            openAddLineTab={openAddLineTab}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            hidePoLineScroll={hidePoLineScroll}
                                            setHidePoLineScroll={setHidePoLineScroll}
                                            scrollToTop={scrollToTop}
                                            calculateMaterialTotalPrice={calculateMaterialTotalPrice}
                                            setIsCreatePoDirty={setIsCreatePoDirty}
                                            isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                            cameFromSavedBom={cameFromSavedBom}
                                            handlePriceIntegration={handlePriceIntegration}
                                            clearErrors={clearErrors}
                                            updateLineItem={updateLineItem}
                                            createPoResultCopy={createPoResultCopy}
                                            setCreatePoResultCopy={setCreatePoResultCopy}
                                            setFormErrors={setFormErrors}
                                            handleFilterFieldsData={handleFilterFieldsData}
                                            willGearUpdate={willGearUpdate}
                                            handleLoadNextItems={handleLoadNextItems}
                                            pageIndex={pageIndex}
                                            lineItemsToLoad={lineItemsToLoad}
                                            debouncedSearchString={debouncedSearchString}
                                            setSelectedProduct={setSelectedProduct}
                                            lineSessionIdChanges={lineSessionIdChanges}
                                            setLineSessionIdChanges={setLineSessionIdChanges}
                                            formErrors={formErrors}
                                        />

                                    }
                                </React.Fragment>
                            )
                        })
                    }
                </tbody>
            </table>
        </div>
    )
})

export default CreatePoTable;